const AccountNav = ({ currentSection, onSectionChange }) => {
  const handleNavClick = (section, e) => {
    e.preventDefault();
    onSectionChange(section);
    // Update URL without page reload
    window.history.pushState({}, '', `/account/${section}`);
  };

  return (
    <nav className="account-nav">
      <a 
        href="#" 
        className={`nav-item ${currentSection === 'profile' ? 'active' : ''}`}
        onClick={(e) => handleNavClick('profile', e)}
      >
        <i className="fas fa-user"></i>
        Profile
      </a>
      <a 
        href="#" 
        className={`nav-item ${currentSection === 'orders' ? 'active' : ''}`}
        onClick={(e) => handleNavClick('orders', e)}
      >
        <i className="fas fa-shopping-bag"></i>
        Orders
      </a>
      <a 
        href="#" 
        className={`nav-item ${currentSection === 'addresses' ? 'active' : ''}`}
        onClick={(e) => handleNavClick('addresses', e)}
      >
        <i className="fas fa-map-marker-alt"></i>
        Addresses
      </a>
    </nav>
  );
}; 