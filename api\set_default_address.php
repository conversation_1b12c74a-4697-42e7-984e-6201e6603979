<?php
header('Content-Type: application/json');
require_once '../config/database.php';

session_start();

if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

$user_id = $_SESSION['user_id'];
$data = json_decode(file_get_contents('php://input'), true);

if (!isset($data['address_id'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Address ID is required']);
    exit;
}

try {
    // Start transaction
    $conn->begin_transaction();

    // First, set all addresses to non-default
    $stmt = $conn->prepare("
        UPDATE addresses 
        SET is_default = 0 
        WHERE user_id = ?
    ");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();

    // Then, set the specified address as default
    $stmt = $conn->prepare("
        UPDATE addresses 
        SET is_default = 1 
        WHERE id = ? AND user_id = ?
    ");
    $stmt->bind_param("ii", $data['address_id'], $user_id);
    $stmt->execute();

    $conn->commit();
    echo json_encode(['success' => true, 'message' => 'Default address updated successfully']);
} catch (Exception $e) {
    $conn->rollback();
    http_response_code(500);
    echo json_encode(['error' => 'Database error']);
}
?> 