<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Redirecting to Admin Panel</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100vh;
            margin: 0;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .container {
            text-align: center;
            padding: 2rem;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            max-width: 500px;
        }
        
        h1 {
            color: hsl(17, 96%, 48%);
            margin-bottom: 1rem;
        }
        
        p {
            margin-bottom: 2rem;
        }
        
        .loader {
            border: 5px solid #f3f3f3;
            border-top: 5px solid hsl(17, 96%, 48%);
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 0 auto 2rem;
        }
        
        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }
        
        .button {
            display: inline-block;
            background-color: hsl(17, 96%, 48%);
            color: white;
            padding: 0.75rem 1.5rem;
            text-decoration: none;
            border-radius: 4px;
            font-weight: bold;
            transition: background-color 0.3s;
        }
        
        .button:hover {
            background-color: hsl(17, 96%, 40%);
        }
    </style>
    <script>
        // Redirect to admin panel after a short delay
        window.onload = function() {
            setTimeout(function() {
                window.location.href = 'admin/index.html';
            }, 2000);
        }
    </script>
</head>

<body>
    <div class="container">
        <h1>Indian Furniture House</h1>
        <div class="loader"></div>
        <p>Redirecting to Admin Panel...</p>
        <a href="admin/index.html" class="button">Go to Admin Panel Now</a>
    </div>
</body>

</html>