// Mock API for testing when server is not available
class MockAPI {
    constructor() {
        this.mockUsers = {
            '<EMAIL>': {
                id: 1,
                email: '<EMAIL>',
                name: 'Test User',
                phone: '+91 **********',
                created_at: '2023-01-01T00:00:00Z'
            }
        };

        this.mockOrders = {
            '<EMAIL>': [{
                    orderId: 'ORD-123456',
                    date: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
                    total: 15999,
                    status: 'delivered',
                    items: [{
                        productName: 'Modern Sofa',
                        quantity: 1,
                        price: 15999,
                        productImage: './assets/images/products/sofa-1.jpg'
                    }]
                },
                {
                    orderId: 'ORD-123457',
                    date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
                    total: 8999,
                    status: 'processing',
                    items: [{
                        productName: 'Dining Table',
                        quantity: 1,
                        price: 8999,
                        productImage: './assets/images/products/table-1.jpg'
                    }]
                }
            ]
        };

        this.mockAddresses = {
            '<EMAIL>': [{
                id: 1,
                fullName: 'Test User',
                address: '123 Main Street',
                city: 'Mumbai',
                state: 'Maharashtra',
                zip: '400001',
                phone: '+91 **********',
                isDefault: true
            }]
        };

        // Install fetch interceptor
        this.installInterceptor();
    }

    // Install fetch interceptor to handle API requests when server is down
    installInterceptor() {
        const originalFetch = window.fetch;
        const mockAPI = this;

        window.fetch = function(url, options) {
            // Only intercept API requests
            if (typeof url === 'string' && url.includes('/api/')) {
                console.log('MockAPI: Intercepting request to', url);

                // Handle user profile endpoint
                if (url.includes('/api/user/profile')) {
                    const emailMatch = url.match(/email=([^&]+)/);
                    if (emailMatch) {
                        const email = decodeURIComponent(emailMatch[1]);
                        console.log('MockAPI: Looking up user with email', email);

                        const user = mockAPI.mockUsers[email] || mockAPI.mockUsers['<EMAIL>'];
                        return Promise.resolve({
                            ok: true,
                            json: () => Promise.resolve(user)
                        });
                    }
                }

                // Handle orders endpoint
                if (url.includes('/api/user/orders')) {
                    const emailMatch = url.match(/email=([^&]+)/);
                    if (emailMatch) {
                        const email = decodeURIComponent(emailMatch[1]);
                        console.log('MockAPI: Looking up orders for email', email);

                        const orders = mockAPI.mockOrders[email] || mockAPI.mockOrders['<EMAIL>'];
                        return Promise.resolve({
                            ok: true,
                            json: () => Promise.resolve(orders)
                        });
                    }
                }

                // Handle addresses endpoint
                if (url.includes('/api/user/addresses')) {
                    const emailMatch = url.match(/email=([^&]+)/);
                    if (emailMatch) {
                        const email = decodeURIComponent(emailMatch[1]);
                        console.log('MockAPI: Looking up addresses for email', email);

                        const addresses = mockAPI.mockAddresses[email] || mockAPI.mockAddresses['<EMAIL>'];
                        return Promise.resolve({
                            ok: true,
                            json: () => Promise.resolve(addresses)
                        });
                    }
                }

                // Handle health check endpoint
                if (url.includes('/api/health')) {
                    console.log('MockAPI: Responding to health check');
                    return Promise.resolve({
                        ok: true,
                        json: () => Promise.resolve({ status: 'healthy', mock: true })
                    });
                }

                // For any other API endpoint, return a 404
                console.log('MockAPI: Unhandled API endpoint', url);
                return Promise.resolve({
                    ok: false,
                    status: 404,
                    statusText: 'Not Found (Mock API)'
                });
            }

            // Pass through to original fetch for non-API requests
            return originalFetch.apply(this, arguments);
        };

        console.log('MockAPI: Fetch interceptor installed');
    }

    // Add a mock user
    addUser(email, userData) {
        this.mockUsers[email] = {
            ...userData,
            email
        };
    }

    // Get current user's email from localStorage
    getCurrentUserEmail() {
        try {
            const userString = localStorage.getItem('user');
            if (!userString) return null;

            const user = JSON.parse(userString);
            return user.email;
        } catch (error) {
            console.error('Error getting current user email:', error);
            return null;
        }
    }

    // Initialize mock data for current user
    initCurrentUser() {
        const email = this.getCurrentUserEmail();
        if (!email) return;

        // If we don't have mock data for this user, create it
        if (!this.mockUsers[email]) {
            console.log('MockAPI: Creating mock data for', email);

            // Get user data from localStorage
            const userString = localStorage.getItem('user');
            const user = JSON.parse(userString);

            // Add mock user
            this.addUser(email, {
                id: Math.floor(Math.random() * 1000) + 1,
                name: user.displayName || user.name || email.split('@')[0],
                phone: user.phoneNumber || user.phone || '+91 **********',
                created_at: new Date().toISOString()
            });

            // Add mock orders
            this.mockOrders[email] = [{
                orderId: 'ORD-' + Math.floor(Math.random() * 1000000),
                date: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
                total: 15999,
                status: 'delivered',
                items: [{
                    productName: 'Modern Sofa',
                    quantity: 1,
                    price: 15999,
                    productImage: './assets/images/products/sofa-1.jpg'
                }]
            }];

            // Add mock addresses
            this.mockAddresses[email] = [{
                id: 1,
                fullName: user.displayName || user.name || email.split('@')[0],
                address: '123 Main Street',
                city: 'Mumbai',
                state: 'Maharashtra',
                zip: '400001',
                phone: user.phoneNumber || user.phone || '+91 **********',
                isDefault: true
            }];

            console.log('MockAPI: Mock data created for', email);
        }
    }

    // Add missing methods that account.js uses
    async getUser(uid) {
        const email = this.getCurrentUserEmail();
        return this.mockUsers[email] || null;
    }

    async getUserOrders(uid) {
        const email = this.getCurrentUserEmail();
        return this.mockOrders[email] || [];
    }

    async getUserAddresses(uid) {
        const email = this.getCurrentUserEmail();
        return this.mockAddresses[email] || [];
    }

    async getUserFavorites(uid) {
        // Mock data for favorites not implemented yet
        return [];
    }

    async addAddress(addressData) {
        const email = this.getCurrentUserEmail();
        if (!email) return null;

        // Generate a unique ID based on timestamp
        const id = Date.now();
        const newAddress = {
            id,
            ...addressData,
            isDefault: false
        };

        if (!this.mockAddresses[email]) {
            this.mockAddresses[email] = [];
        }

        this.mockAddresses[email].push(newAddress);
        return newAddress;
    }
}

// Initialize mock API
const mockAPI = new MockAPI();
mockAPI.initCurrentUser();

export default mockAPI;