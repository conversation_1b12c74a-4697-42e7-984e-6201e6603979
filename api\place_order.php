<?php
header('Content-Type: application/json');
require_once '../config/database.php';
session_start();

if (!isset($_SESSION['user_id'])) {
    echo json_encode(['error' => 'Unauthorized access']);
    exit;
}

$data = json_decode(file_get_contents('php://input'), true);

if (!$data || !isset($data['address']) || !isset($data['payment_method']) || !isset($data['cart'])) {
    echo json_encode(['error' => 'Invalid data']);
    exit;
}

try {
    // Start transaction
    $conn->begin_transaction();

    // Insert order
    $stmt = $conn->prepare("INSERT INTO orders (user_id, address_id, payment_method, total_amount, status, created_at) 
                           VALUES (?, ?, ?, ?, 'pending', NOW())");
    
    $stmt->bind_param("iids", 
        $_SESSION['user_id'],
        $data['address']['id'],
        $data['payment_method'],
        $data['total']
    );
    
    $stmt->execute();
    $order_id = $conn->insert_id;

    // Insert order items
    $stmt = $conn->prepare("INSERT INTO order_items (order_id, product_id, quantity, price) 
                           VALUES (?, ?, ?, ?)");
    
    foreach ($data['cart'] as $item) {
        $stmt->bind_param("iiid",
            $order_id,
            $item['id'],
            $item['quantity'],
            $item['price']
        );
        $stmt->execute();
    }

    // Commit transaction
    $conn->commit();

    echo json_encode([
        'success' => true,
        'order_id' => $order_id,
        'message' => 'Order placed successfully'
    ]);

} catch (Exception $e) {
    // Rollback transaction on error
    $conn->rollback();
    echo json_encode(['error' => 'Failed to place order: ' . $e->getMessage()]);
}

$conn->close();
?>
