<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Checkout - Indian Furniture House</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Add Razorpay Checkout Script -->
    <script src="https://checkout.razorpay.com/v1/checkout.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', sans-serif;
        }
        
         :root {
            --primary: #ff7f50;
            --primary-dark: #ff6347;
            --bg-dark: #1a1a1a;
            --text-light: #ffffff;
            --text-gray: rgba(255, 255, 255, 0.7);
            --card-bg: rgba(255, 255, 255, 0.03);
            --card-border: rgba(255, 255, 255, 0.1);
        }
        
        html {
            height: 100%;
        }
        
        body {
            min-height: 100%;
            background: var(--bg-dark);
            position: relative;
            padding: 2rem;
            color: var(--text-light);
        }
        /* Modern animated background */
        
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at 20% 35%, rgba(255, 127, 80, 0.15) 0%, transparent 50%), radial-gradient(circle at 75% 44%, rgba(255, 99, 71, 0.15) 0%, transparent 50%), radial-gradient(circle at 45% 85%, rgba(255, 160, 122, 0.15) 0%, transparent 50%);
            filter: blur(5px);
            z-index: 0;
        }
        
        .grid-background {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: linear-gradient(rgba(255, 127, 80, 0.05) 1px, transparent 1px), linear-gradient(90deg, rgba(255, 127, 80, 0.05) 1px, transparent 1px);
            background-size: 50px 50px;
            z-index: 1;
            perspective: 1000px;
            transform-style: preserve-3d;
            animation: gridMove 20s linear infinite;
        }
        
        @keyframes gridMove {
            0% {
                transform: translateZ(0) rotate(0deg);
            }
            100% {
                transform: translateZ(100px) rotate(360deg);
            }
        }
        
        .page-wrapper {
            position: relative;
            z-index: 2;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .checkout-container {
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 2rem;
        }
        
        .checkout-form {
            background: var(--card-bg);
            backdrop-filter: blur(10px);
            border: 1px solid var(--card-border);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .form-header {
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--card-border);
        }
        
        .form-header h1 {
            font-size: 1.8rem;
            color: var(--text-light);
        }
        
        .form-section {
            margin-bottom: 2rem;
        }
        
        .form-section h2 {
            font-size: 1.2rem;
            color: var(--text-light);
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .form-section h2 i {
            color: var(--primary);
        }
        
        .form-row {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
            margin-bottom: 1rem;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: var(--text-gray);
            font-size: 0.9rem;
        }
        
        .form-group input,
        .form-group select {
            width: 100%;
            padding: 0.75rem;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--card-border);
            border-radius: 10px;
            color: var(--text-light);
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 2px rgba(255, 127, 80, 0.1);
        }
        
        .form-group select option {
            background: var(--bg-dark);
            color: var(--text-light);
        }
        
        .order-summary {
            background: var(--card-bg);
            backdrop-filter: blur(10px);
            border: 1px solid var(--card-border);
            border-radius: 20px;
            padding: 2rem;
            height: fit-content;
            position: sticky;
            top: 2rem;
        }
        
        .summary-header {
            font-size: 1.4rem;
            color: var(--text-light);
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--card-border);
        }
        
        .order-items {
            margin-bottom: 1.5rem;
        }
        
        .order-item {
            display: flex;
            gap: 1rem;
            padding: 1rem 0;
            border-bottom: 1px solid var(--card-border);
        }
        
        .item-image {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 8px;
            border: 1px solid var(--card-border);
        }
        
        .item-details {
            flex: 1;
        }
        
        .item-name {
            color: var(--text-light);
            margin-bottom: 0.25rem;
        }
        
        .item-price {
            color: var(--text-gray);
            font-size: 0.9rem;
        }
        
        .summary-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 1rem;
            color: var(--text-gray);
        }
        
        .summary-total {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--text-light);
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 1px solid var(--card-border);
        }
        
        .place-order-btn {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            color: white;
            border: none;
            width: 100%;
            padding: 1rem;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 1.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }
        
        .place-order-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 127, 80, 0.3);
        }
        
        .payment-methods {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
            margin-bottom: 1.5rem;
        }
        
        .payment-method {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--card-border);
            border-radius: 10px;
            padding: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .payment-method:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
        }
        
        .payment-method.selected {
            border-color: var(--primary);
            background: rgba(255, 127, 80, 0.1);
        }
        
        .payment-method i {
            font-size: 1.5rem;
            color: var(--primary);
        }
        
        @media (max-width: 768px) {
            body {
                padding: 1rem;
            }
            .checkout-container {
                grid-template-columns: 1fr;
            }
            .form-row {
                grid-template-columns: 1fr;
            }
            .payment-methods {
                grid-template-columns: 1fr;
            }
            .order-summary {
                position: static;
                margin-top: 2rem;
            }
        }
        /* Header Styles */
        
        .header {
            background: rgba(255, 255, 255, 0.03);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid var(--card-border);
            padding: 1rem 0;
            margin-bottom: 2rem;
        }
        
        .brand-container {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 1rem;
            text-decoration: none;
            color: var(--text-light);
        }
        
        .logo-modern {
            display: flex;
            align-items: center;
            gap: 1.5rem;
        }
        
        .sofa-icon {
            position: relative;
            font-size: 2.5rem;
            color: var(--primary);
        }
        
        .icon-glow {
            position: absolute;
            inset: 0;
            background: radial-gradient(circle, rgba(255, 127, 80, 0.4) 0%, transparent 70%);
            filter: blur(8px);
            animation: pulse 2s infinite;
        }
        
        .brand-text {
            position: relative;
        }
        
        .text-full {
            font-size: 1.5rem;
            font-weight: 600;
            background: linear-gradient(45deg, var(--text-light) 30%, rgba(255, 127, 80, 0.8) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            letter-spacing: 1px;
        }
        
        .text-shine {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            animation: shine 3s infinite;
        }
        
        @keyframes pulse {
            0%,
            100% {
                transform: scale(1);
                opacity: 0.5;
            }
            50% {
                transform: scale(1.2);
                opacity: 1;
            }
        }
        
        @keyframes shine {
            0% {
                transform: translateX(-100%);
            }
            100% {
                transform: translateX(100%);
            }
        }
        /* Additional styles for Razorpay button */
        
        .razorpay-payment-button {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            color: white;
            border: none;
            width: 100%;
            padding: 1rem;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 1.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }
        
        .razorpay-payment-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 127, 80, 0.3);
        }
        /* Loading spinner */
        
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, .3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
            margin-right: 10px;
        }
        
        @keyframes spin {
            to {
                transform: rotate(360deg);
            }
        }
    </style>
</head>

<body>
    <div class="grid-background"></div>

    <div class="page-wrapper">
        <!-- Brand Header -->
        <header class="header">
            <a href="index.html" class="brand-container">
                <div class="logo-modern">
                    <div class="sofa-icon">
                        <i class="fas fa-couch"></i>
                        <div class="icon-glow"></div>
                    </div>
                    <div class="brand-text">
                        <span class="text-full">INDIAN FURNITURE HOUSE</span>
                        <div class="text-shine"></div>
                    </div>
                </div>
            </a>
        </header>

        <div class="checkout-container">
            <div class="checkout-form">
                <div class="form-header">
                    <h1>Checkout</h1>
                </div>

                <form id="checkoutForm">
                    <div class="form-section">
                        <h2><i class="fas fa-user"></i> Personal Information</h2>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="fullName">Full Name</label>
                                <input type="text" id="fullName" required>
                            </div>
                            <div class="form-group">
                                <label for="email">Email</label>
                                <input type="email" id="email" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="phone">Phone Number</label>
                            <input type="tel" id="phone" required>
                        </div>
                    </div>

                    <div class="form-section">
                        <h2><i class="fas fa-map-marker-alt"></i> Shipping Address</h2>
                        <div class="form-group">
                            <label for="address">Street Address</label>
                            <input type="text" id="address" required>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="city">City</label>
                                <input type="text" id="city" required>
                            </div>
                            <div class="form-group">
                                <label for="state">State</label>
                                <select id="state" required>
                  <option value="">Select State</option>
                  <option value="AN">Andaman and Nicobar Islands</option>
                  <option value="AP">Andhra Pradesh</option>
                  <option value="AR">Arunachal Pradesh</option>
                  <option value="AS">Assam</option>
                  <option value="BR">Bihar</option>
                  <option value="CH">Chandigarh</option>
                  <option value="CT">Chhattisgarh</option>
                  <option value="DN">Dadra and Nagar Haveli</option>
                  <option value="DD">Daman and Diu</option>
                  <option value="DL">Delhi</option>
                  <option value="GA">Goa</option>
                  <option value="GJ">Gujarat</option>
                  <option value="HR">Haryana</option>
                  <option value="HP">Himachal Pradesh</option>
                  <option value="JK">Jammu and Kashmir</option>
                  <option value="JH">Jharkhand</option>
                  <option value="KA">Karnataka</option>
                  <option value="KL">Kerala</option>
                  <option value="LA">Ladakh</option>
                  <option value="LD">Lakshadweep</option>
                  <option value="MP">Madhya Pradesh</option>
                  <option value="MH">Maharashtra</option>
                  <option value="MN">Manipur</option>
                  <option value="ML">Meghalaya</option>
                  <option value="MZ">Mizoram</option>
                  <option value="NL">Nagaland</option>
                  <option value="OR">Odisha</option>
                  <option value="PY">Puducherry</option>
                  <option value="PB">Punjab</option>
                  <option value="RJ">Rajasthan</option>
                  <option value="SK">Sikkim</option>
                  <option value="TN">Tamil Nadu</option>
                  <option value="TG">Telangana</option>
                  <option value="TR">Tripura</option>
                  <option value="UP">Uttar Pradesh</option>
                  <option value="UT">Uttarakhand</option>
                  <option value="WB">West Bengal</option>
                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="pincode">PIN Code</label>
                            <input type="text" id="pincode" required>
                        </div>
                    </div>

                    <div class="form-section">
                        <h2><i class="fas fa-credit-card"></i> Payment Information</h2>
                        <p style="color: var(--text-gray); margin-bottom: 1rem;">
                            Secure payment powered by Razorpay. Your payment information is encrypted and secure.
                        </p>
                        <div id="payment-message" style="display: none; margin-top: 1rem; padding: 1rem; border-radius: 8px;"></div>
                    </div>
                </form>
            </div>

            <div class="order-summary">
                <h2 class="summary-header">Order Summary</h2>
                <div class="order-items" id="orderItems">
                    <!-- Order items will be loaded here -->
                </div>
                <div class="summary-item">
                    <span>Subtotal</span>
                    <span>₹<span id="subtotal">0.00</span></span>
                </div>
                <div class="summary-item">
                    <span>Shipping</span>
                    <span>₹<span id="shipping">0.00</span></span>
                </div>
                <div class="summary-item">
                    <span>Tax (18%)</span>
                    <span>₹<span id="tax">0.00</span></span>
                </div>
                <div class="summary-item summary-total">
                    <span>Total</span>
                    <span>₹<span id="total">0.00</span></span>
                </div>
                <button id="rzp-button" class="place-order-btn" onclick="initiatePayment()">
                    <i class="fas fa-lock"></i>
                    Pay Securely
                </button>
            </div>
        </div>
    </div>

    <script src="./assets/js/products.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            loadOrderSummary();
            loadUserData();
        });

        function loadOrderSummary() {
            try {
                const cart = JSON.parse(localStorage.getItem('cart')) || [];
                const orderItems = document.getElementById('orderItems');

                if (!cart || cart.length === 0) {
                    window.location.href = 'cart.html';
                    return;
                }

                const products = window.products;
                if (!products) {
                    throw new Error('Products data not found');
                }

                orderItems.innerHTML = '';
                let subtotal = 0;

                cart.forEach(cartItem => {
                    const product = products.find(p => p.id === cartItem.id);
                    if (!product) return;

                    const price = parseFloat(product.price) || 0;
                    const quantity = parseInt(cartItem.quantity) || 0;
                    const itemTotal = price * quantity;
                    subtotal += itemTotal;

                    const itemElement = document.createElement('div');
                    itemElement.className = 'order-item';
                    itemElement.innerHTML = `
            <img src="${product.image}" alt="${product.name}" class="item-image">
            <div class="item-details">
              <div class="item-name">${product.name} × ${quantity}</div>
              <div class="item-price">₹${itemTotal.toFixed(2)}</div>
          </div>
        `;
                    orderItems.appendChild(itemElement);
                });

                updateSummary(subtotal);
            } catch (error) {
                console.error('Error loading order summary:', error);
            }
        }

        function loadUserData() {
            const user = JSON.parse(localStorage.getItem('user'));
            if (user) {
                document.getElementById('fullName').value = user.full_name || '';
                document.getElementById('email').value = user.email || '';
                document.getElementById('phone').value = user.phone || '';
            }
        }

        function updateSummary(subtotal) {
            try {
                subtotal = parseFloat(subtotal) || 0;
                const shipping = subtotal > 0 ? 50 : 0;
                const tax = subtotal * 0.18;
                const total = subtotal + shipping + tax;

                document.getElementById('subtotal').textContent = subtotal.toFixed(2);
                document.getElementById('shipping').textContent = shipping.toFixed(2);
                document.getElementById('tax').textContent = tax.toFixed(2);
                document.getElementById('total').textContent = total.toFixed(2);
            } catch (error) {
                console.error('Error updating summary:', error);
            }
        }

        function selectPaymentMethod(element, method) {
            document.querySelectorAll('.payment-method').forEach(el => {
                el.classList.remove('selected');
            });
            element.classList.add('selected');
        }

        // Initialize Razorpay configuration
        const razorpayConfig = {
            key: "rzp_test_0idC7y8TPZI6Og", // Your Razorpay Key ID
            currency: "INR",
            name: "Indian Furniture House",
            description: "Purchase Payment",
            image: "https://your-logo-url.com/logo.png", // Add your logo URL
            theme: {
                color: "#ff7f50"
            }
        };

        async function initiatePayment() {
            const form = document.getElementById('checkoutForm');
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            const button = document.getElementById('rzp-button');
            button.disabled = true;
            button.innerHTML = '<span class="loading-spinner"></span>Processing...';

            try {
                // Get order details
                const orderData = {
                    orderId: 'ORD' + Date.now(),
                    fullName: document.getElementById('fullName').value,
                    email: document.getElementById('email').value,
                    phone: document.getElementById('phone').value,
                    address: document.getElementById('address').value,
                    city: document.getElementById('city').value,
                    state: document.getElementById('state').value,
                    zip: document.getElementById('pincode').value,
                    cart: JSON.parse(localStorage.getItem('cart')),
                    orderDate: new Date().toISOString(),
                    total: parseFloat(document.getElementById('total').textContent)
                };

                // Convert total to paise (Razorpay expects amount in smallest currency unit)
                const amountInPaise = Math.round(orderData.total * 100);

                // Configure Razorpay options
                const options = {
                    ...razorpayConfig,
                    amount: amountInPaise,
                    prefill: {
                        name: orderData.fullName,
                        email: orderData.email,
                        contact: orderData.phone
                    },
                    handler: function(response) {
                        handlePaymentSuccess(response, orderData);
                    },
                    modal: {
                        ondismiss: function() {
                            button.disabled = false;
                            button.innerHTML = '<i class="fas fa-lock"></i>Pay Securely';
                        }
                    }
                };

                // Initialize Razorpay
                const rzp = new Razorpay(options);
                rzp.open();

            } catch (error) {
                console.error('Payment initiation error:', error);
                showPaymentMessage('error', 'Failed to initiate payment. Please try again.');
                button.disabled = false;
                button.innerHTML = '<i class="fas fa-lock"></i>Pay Securely';
            }
        }

        function handlePaymentSuccess(response, orderData) {
            try {
                // Add payment details to order data
                const finalOrderData = {
                    ...orderData,
                    paymentId: response.razorpay_payment_id,
                    paymentStatus: 'paid'
                };

                // Store order data
                sessionStorage.setItem(`order_${orderData.orderId}`, JSON.stringify(finalOrderData));

                // Clear cart
                localStorage.removeItem('cart');

                // Show success message
                showPaymentMessage('success', 'Payment successful! Redirecting to confirmation page...');

                // Redirect to confirmation page
                setTimeout(() => {
                    window.location.href = `order-confirmation.html?orderId=${orderData.orderId}`;
                }, 1500);

            } catch (error) {
                console.error('Payment success handling error:', error);
                showPaymentMessage('error', 'Payment was successful but there was an error processing your order. Please contact support.');
            }
        }

        function showPaymentMessage(type, message) {
            const messageDiv = document.getElementById('payment-message');
            messageDiv.style.display = 'block';
            messageDiv.style.backgroundColor = type === 'error' ? '#dc3545' : '#28a745';
            messageDiv.style.color = 'white';
            messageDiv.textContent = message;
        }
    </script>
</body>

</html>