const Addresses = () => {
  const [addresses, setAddresses] = React.useState([]);
  const [loading, setLoading] = React.useState(true);
  const [showAddForm, setShowAddForm] = React.useState(false);
  const [editingAddress, setEditingAddress] = React.useState(null);
  const [formData, setFormData] = React.useState({
    addressLine: '',
    city: '',
    state: '',
    zipCode: '',
    isDefault: false
  });

  React.useEffect(() => {
    loadAddresses();
  }, []);

  const loadAddresses = () => {
    try {
      const user = JSON.parse(localStorage.getItem('user'));
      if (!user) {
        window.location.href = 'login.html';
        return;
      }

      setAddresses(user.addresses || []);
    } catch (error) {
      console.error('Error loading addresses:', error);
      if (window.showNotification) {
        window.showNotification('Error loading addresses', 'error');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const user = JSON.parse(localStorage.getItem('user'));
      if (!user) return;

      let updatedAddresses = [...(user.addresses || [])];
      
      if (editingAddress) {
        // Update existing address
        const index = updatedAddresses.findIndex(addr => addr.id === editingAddress.id);
        if (index !== -1) {
          updatedAddresses[index] = { ...formData, id: editingAddress.id };
        }
      } else {
        // Add new address
        const newAddress = {
          ...formData,
          id: Date.now().toString(),
          isDefault: formData.isDefault || updatedAddresses.length === 0
        };
        updatedAddresses.push(newAddress);
      }

      // If new address is set as default, update other addresses
      if (formData.isDefault) {
        updatedAddresses = updatedAddresses.map(addr => ({
          ...addr,
          isDefault: addr.id === (editingAddress?.id || newAddress.id)
        }));
      }

      // Update user data in localStorage
      user.addresses = updatedAddresses;
      localStorage.setItem('user', JSON.stringify(user));

      // Update addresses in state
      setAddresses(updatedAddresses);
      resetForm();

      if (window.showNotification) {
        window.showNotification(
          `Address ${editingAddress ? 'updated' : 'added'} successfully`,
          'success'
        );
      }
    } catch (error) {
      console.error('Error saving address:', error);
      if (window.showNotification) {
        window.showNotification('Error saving address', 'error');
      }
    }
  };

  const handleEdit = (address) => {
    setEditingAddress(address);
    setFormData({
      addressLine: address.addressLine,
      city: address.city,
      state: address.state,
      zipCode: address.zipCode,
      isDefault: address.isDefault
    });
    setShowAddForm(true);
  };

  const handleDelete = async (addressId) => {
    if (!window.confirm('Are you sure you want to delete this address?')) return;

    try {
      const user = JSON.parse(localStorage.getItem('user'));
      if (!user) return;

      const updatedAddresses = user.addresses.filter(addr => addr.id !== addressId);
      
      // If deleted address was default, make the first remaining address default
      if (updatedAddresses.length > 0 && user.addresses.find(addr => addr.id === addressId)?.isDefault) {
        updatedAddresses[0].isDefault = true;
      }

      user.addresses = updatedAddresses;
      localStorage.setItem('user', JSON.stringify(user));
      setAddresses(updatedAddresses);

      if (window.showNotification) {
        window.showNotification('Address deleted successfully', 'success');
      }
    } catch (error) {
      console.error('Error deleting address:', error);
      if (window.showNotification) {
        window.showNotification('Error deleting address', 'error');
      }
    }
  };

  const handleSetDefault = async (addressId) => {
    try {
      const user = JSON.parse(localStorage.getItem('user'));
      if (!user) return;

      const updatedAddresses = user.addresses.map(addr => ({
        ...addr,
        isDefault: addr.id === addressId
      }));

      user.addresses = updatedAddresses;
      localStorage.setItem('user', JSON.stringify(user));
      setAddresses(updatedAddresses);

      if (window.showNotification) {
        window.showNotification('Default address updated', 'success');
      }
    } catch (error) {
      console.error('Error setting default address:', error);
      if (window.showNotification) {
        window.showNotification('Error setting default address', 'error');
      }
    }
  };

  const resetForm = () => {
    setFormData({
      addressLine: '',
      city: '',
      state: '',
      zipCode: '',
      isDefault: false
    });
    setEditingAddress(null);
    setShowAddForm(false);
  };

  if (loading) {
    return <div className="addresses-loading">Loading addresses...</div>;
  }

  return (
    <div className="addresses-container">
      <div className="addresses-header">
        <h2>My Addresses</h2>
        <button 
          className="add-address-btn"
          onClick={() => setShowAddForm(true)}
        >
          Add New Address
        </button>
      </div>

      {showAddForm && (
        <div className="address-form-container">
          <form onSubmit={handleSubmit} className="address-form">
            <div className="form-group">
              <label htmlFor="addressLine">Address Line</label>
              <input
                type="text"
                id="addressLine"
                name="addressLine"
                value={formData.addressLine}
                onChange={handleInputChange}
                required
              />
            </div>

            <div className="form-row">
              <div className="form-group">
                <label htmlFor="city">City</label>
                <input
                  type="text"
                  id="city"
                  name="city"
                  value={formData.city}
                  onChange={handleInputChange}
                  required
                />
              </div>

              <div className="form-group">
                <label htmlFor="state">State</label>
                <input
                  type="text"
                  id="state"
                  name="state"
                  value={formData.state}
                  onChange={handleInputChange}
                  required
                />
              </div>

              <div className="form-group">
                <label htmlFor="zipCode">ZIP Code</label>
                <input
                  type="text"
                  id="zipCode"
                  name="zipCode"
                  value={formData.zipCode}
                  onChange={handleInputChange}
                  required
                />
              </div>
            </div>

            <div className="form-group checkbox">
              <label>
                <input
                  type="checkbox"
                  name="isDefault"
                  checked={formData.isDefault}
                  onChange={handleInputChange}
                />
                Set as default address
              </label>
            </div>

            <div className="form-actions">
              <button type="submit" className="save-btn">
                {editingAddress ? 'Update Address' : 'Add Address'}
              </button>
              <button 
                type="button" 
                className="cancel-btn"
                onClick={resetForm}
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      )}

      <div className="addresses-list">
        {addresses.length === 0 ? (
          <div className="no-addresses">
            <p>You haven't added any addresses yet.</p>
          </div>
        ) : (
          addresses.map(address => (
            <div 
              key={address.id} 
              className={`address-card ${address.isDefault ? 'default' : ''}`}
            >
              {address.isDefault && (
                <span className="default-badge">Default</span>
              )}
              <div className="address-content">
                <p className="address-line">{address.addressLine}</p>
                <p className="address-details">
                  {address.city}, {address.state} {address.zipCode}
                </p>
              </div>
              <div className="address-actions">
                {!address.isDefault && (
                  <button 
                    onClick={() => handleSetDefault(address.id)}
                    className="set-default-btn"
                  >
                    Set as Default
                  </button>
                )}
                <button 
                  onClick={() => handleEdit(address)}
                  className="edit-btn"
                >
                  Edit
                </button>
                <button 
                  onClick={() => handleDelete(address.id)}
                  className="delete-btn"
                >
                  Delete
                </button>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
}; 