import express from 'express';
import mysql from 'mysql2/promise';

const router = express.Router();

// Database configuration
const dbConfig = {
    host: 'localhost',
    user: 'root',
    password: '',
    database: 'indian_furniture_house'
};

// Create connection pool
const pool = mysql.createPool(dbConfig);

// Register new user
router.post('/register', async(req, res) => {
    try {
        const { email, name, phone } = req.body;

        // Check if user already exists
        const [existingUser] = await pool.query(
            'SELECT id FROM users WHERE email = ?', [email]
        );

        if (existingUser.length > 0) {
            return res.status(400).json({ message: 'User already exists' });
        }

        // Insert new user
        const insertQuery = `
            INSERT INTO users (email, name, phone_number)
            VALUES (?, ?, ?)
        `;

        const [result] = await pool.query(insertQuery, [email, name, phone]);

        // Fetch the created user
        const [newUser] = await pool.query(
            'SELECT id, email, name, phone_number as phone, created_at FROM users WHERE id = ?', [result.insertId]
        );

        res.status(201).json(newUser[0]);
    } catch (error) {
        console.error('Error registering user:', error.message);
        res.status(500).json({ message: 'Failed to register user' });
    }
});

// Get user profile by email
router.get('/profile', async(req, res) => {
    try {
        const { email } = req.query;

        if (!email) {
            return res.status(400).json({ message: 'Email is required' });
        }

        const query = `
            SELECT id, email, name, phone_number as phone, created_at
            FROM users
            WHERE email = ?
        `;

        const [results] = await pool.query(query, [email]);

        if (results.length === 0) {
            return res.status(404).json({ message: 'User not found' });
        }

        res.json(results[0]);
    } catch (error) {
        console.error('Error fetching user profile:', error.message);
        res.status(500).json({ message: 'Failed to fetch user profile' });
    }
});

// Update user profile
router.put('/profile', async(req, res) => {
    try {
        const { email } = req.query;
        const { name, phone } = req.body;

        if (!email) {
            return res.status(400).json({ message: 'Email is required' });
        }

        const updateQuery = `
            UPDATE users 
            SET name = ?, phone_number = ?
            WHERE email = ?
        `;

        await pool.query(updateQuery, [name, phone, email]);

        // Fetch updated user data
        const selectQuery = `
            SELECT id, email, name, phone_number as phone, created_at
            FROM users
            WHERE email = ?
        `;

        const [results] = await pool.query(selectQuery, [email]);

        if (results.length === 0) {
            return res.status(404).json({ message: 'User not found' });
        }

        res.json(results[0]);
    } catch (error) {
        console.error('Error updating user profile:', error.message);
        res.status(500).json({ message: 'Failed to update user profile' });
    }
});

// Get user addresses
router.get('/addresses', async(req, res) => {
    try {
        const { email } = req.query;

        if (!email) {
            return res.status(400).json({ message: 'Email is required' });
        }

        const query = `
            SELECT a.* 
            FROM user_addresses a
            JOIN users u ON a.userId = u.id
            WHERE u.email = ?
            ORDER BY a.isDefault DESC, a.createdAt DESC
        `;

        const [results] = await pool.query(query, [email]);
        res.json(results);
    } catch (error) {
        console.error('Error fetching addresses:', error.message);
        res.status(500).json({ message: 'Failed to fetch addresses' });
    }
});

// Add new address
router.post('/addresses', async(req, res) => {
    try {
        const { email } = req.query;
        const { address, city, state, zip, isDefault } = req.body;

        if (!email) {
            return res.status(400).json({ message: 'Email is required' });
        }

        // Get user ID first
        const [userResults] = await pool.query(
            'SELECT id FROM users WHERE email = ?', [email]
        );

        if (userResults.length === 0) {
            return res.status(404).json({ message: 'User not found' });
        }

        const userId = userResults[0].id;

        // If this is the default address, unset any existing default
        if (isDefault) {
            await pool.query(
                'UPDATE user_addresses SET isDefault = 0 WHERE userId = ?', [userId]
            );
        }

        const insertQuery = `
            INSERT INTO user_addresses (userId, address, city, state, zip, isDefault)
            VALUES (?, ?, ?, ?, ?, ?)
        `;

        const [result] = await pool.query(insertQuery, [
            userId,
            address,
            city,
            state,
            zip,
            isDefault ? 1 : 0
        ]);

        res.status(201).json({
            id: result.insertId,
            userId,
            address,
            city,
            state,
            zip,
            isDefault
        });
    } catch (error) {
        console.error('Error adding address:', error.message);
        res.status(500).json({ message: 'Failed to add address' });
    }
});

// Update address
router.put('/addresses/:id', async(req, res) => {
    try {
        const { id } = req.params;
        const { email } = req.query;
        const { address, city, state, zip, isDefault } = req.body;

        if (!email) {
            return res.status(400).json({ message: 'Email is required' });
        }

        // Get user ID and verify ownership
        const [userResults] = await pool.query(
            'SELECT id FROM users WHERE email = ?', [email]
        );

        if (userResults.length === 0) {
            return res.status(404).json({ message: 'User not found' });
        }

        const userId = userResults[0].id;

        // Verify address belongs to user
        const [addressCheck] = await pool.query(
            'SELECT id FROM user_addresses WHERE id = ? AND userId = ?', [id, userId]
        );

        if (addressCheck.length === 0) {
            return res.status(403).json({ message: 'Address not found or access denied' });
        }

        // If this is the default address, unset any existing default
        if (isDefault) {
            await pool.query(
                'UPDATE user_addresses SET isDefault = 0 WHERE userId = ? AND id != ?', [userId, id]
            );
        }

        const updateQuery = `
            UPDATE user_addresses 
            SET address = ?, city = ?, state = ?, zip = ?, isDefault = ?
            WHERE id = ? AND userId = ?
        `;

        await pool.query(updateQuery, [
            address,
            city,
            state,
            zip,
            isDefault ? 1 : 0,
            id,
            userId
        ]);

        // Fetch updated address
        const [updatedAddress] = await pool.query(
            'SELECT * FROM user_addresses WHERE id = ?', [id]
        );

        res.json(updatedAddress[0]);
    } catch (error) {
        console.error('Error updating address:', error.message);
        res.status(500).json({ message: 'Failed to update address' });
    }
});

// Delete address
router.delete('/addresses/:id', async(req, res) => {
    try {
        const { id } = req.params;
        const { email } = req.query;

        if (!email) {
            return res.status(400).json({ message: 'Email is required' });
        }

        // Get user ID and verify ownership
        const [userResults] = await pool.query(
            'SELECT id FROM users WHERE email = ?', [email]
        );

        if (userResults.length === 0) {
            return res.status(404).json({ message: 'User not found' });
        }

        const userId = userResults[0].id;

        // Verify address belongs to user
        const [addressCheck] = await pool.query(
            'SELECT id, isDefault FROM user_addresses WHERE id = ? AND userId = ?', [id, userId]
        );

        if (addressCheck.length === 0) {
            return res.status(403).json({ message: 'Address not found or access denied' });
        }

        // Don't allow deletion of the default address
        if (addressCheck[0].isDefault) {
            return res.status(400).json({ message: 'Cannot delete default address' });
        }

        await pool.query(
            'DELETE FROM user_addresses WHERE id = ? AND userId = ?', [id, userId]
        );

        res.json({ message: 'Address deleted successfully' });
    } catch (error) {
        console.error('Error deleting address:', error.message);
        res.status(500).json({ message: 'Failed to delete address' });
    }
});

// Get user orders
router.get('/orders', async(req, res) => {
    try {
        const { email } = req.query;

        if (!email) {
            return res.status(400).json({ message: 'Email is required' });
        }

        const query = `
            SELECT o.*, a.address, a.city, a.state, a.zip
            FROM orders o
            JOIN users u ON o.userId = u.id
            LEFT JOIN user_addresses a ON o.shippingAddressId = a.id
            WHERE u.email = ?
            ORDER BY o.createdAt DESC
        `;

        const [results] = await pool.query(query, [email]);
        res.json(results);
    } catch (error) {
        console.error('Error fetching orders:', error.message);
        res.status(500).json({ message: 'Failed to fetch orders' });
    }
});

export default router;