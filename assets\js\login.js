// Import Firebase modules
import { initializeApp } from "https://www.gstatic.com/firebasejs/11.0.2/firebase-app.js";
import {
    getAuth,
    signInWithEmailAndPassword,
    GoogleAuthProvider,
    FacebookAuthProvider,
    signInWithPopup
} from "https://www.gstatic.com/firebasejs/11.0.2/firebase-auth.js";
import { firebaseConfig } from "./firebase-config.js";
import { login } from './auth.js';

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const auth = getAuth(app);

// API base URL
const API_BASE_URL = 'http://localhost:3000/api';

function showError(message) {
    const errorDiv = document.createElement('div');
    errorDiv.className = 'error-message';
    errorDiv.style.cssText = `
        color: #dc3545;
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        border-radius: 4px;
        padding: 10px;
        margin: 10px 0;
        text-align: center;
    `;
    errorDiv.textContent = message;

    // Remove any existing error message
    const existingError = document.querySelector('.error-message');
    if (existingError) {
        existingError.remove();
    }

    // Insert error message before the form
    const form = document.getElementById('login-form');
    form.parentNode.insertBefore(errorDiv, form);
}

function showSuccess(message) {
    const successDiv = document.createElement('div');
    successDiv.className = 'success-message';
    successDiv.style.cssText = `
        color: #155724;
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        border-radius: 4px;
        padding: 10px;
        margin: 10px 0;
        text-align: center;
    `;
    successDiv.textContent = message;

    // Remove any existing success message
    const existingSuccess = document.querySelector('.success-message');
    if (existingSuccess) {
        existingSuccess.remove();
    }

    // Insert success message before the form
    const form = document.getElementById('login-form');
    form.parentNode.insertBefore(successDiv, form);
}

document.addEventListener('DOMContentLoaded', function() {
    // Button animations
    document.querySelectorAll(".button, .social-button").forEach((button) => {
        button.addEventListener("mousedown", () => button.style.transform = "scale(0.95)");
        button.addEventListener("mouseup", () => button.style.transform = "scale(1)");
        button.addEventListener("mouseleave", () => button.style.transform = "scale(1)");
    });

    const loginForm = document.getElementById('login-form');
    const errorMessage = document.getElementById('error-message');

    // Check if user is already logged in
    const isLoggedIn = localStorage.getItem('isLoggedIn') === 'true';
    if (isLoggedIn) {
        window.location.href = 'account.html';
        return;
    }

    loginForm.addEventListener('submit', async function(e) {
        e.preventDefault();

        const email = document.getElementById('email').value;
        const password = document.getElementById('password').value;

        try {
            // Show loading state
            const submitBtn = loginForm.querySelector('button[type="submit"]');
            const originalBtnText = submitBtn.textContent;
            submitBtn.disabled = true;
            submitBtn.textContent = 'Logging in...';

            // Attempt login
            await login(email, password);

            // Redirect to account page
            window.location.href = 'account.html';
        } catch (error) {
            console.error('Login error:', error);
            errorMessage.textContent = error.message || 'Failed to login. Please try again.';
            errorMessage.style.display = 'block';

            // Reset button
            const submitBtn = loginForm.querySelector('button[type="submit"]');
            submitBtn.disabled = false;
            submitBtn.textContent = 'Login';
        }
    });

    // Google sign-in
    const googleSignInBtn = document.getElementById('google-signin');
    if (googleSignInBtn) {
        googleSignInBtn.addEventListener('click', async function(event) {
            event.preventDefault();
            try {
                const provider = new GoogleAuthProvider();
                const result = await signInWithPopup(auth, provider);
                const firebaseUser = result.user;

                // Sync with SQL database
                const response = await fetch(`${API_BASE_URL}/users/auth`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        firebase_uid: firebaseUser.uid,
                        email: firebaseUser.email,
                        display_name: firebaseUser.displayName,
                        photo_url: firebaseUser.photoURL
                    })
                });

                if (!response.ok) {
                    throw new Error('Failed to sync user with database');
                }

                const userData = await response.json();

                localStorage.setItem('user', JSON.stringify({
                    ...userData,
                    firebase_uid: firebaseUser.uid
                }));
                localStorage.setItem('isLoggedIn', 'true');

                window.location.href = 'account.html';
            } catch (error) {
                console.error('Google sign-in error:', error);
                alert('Google sign-in failed: ' + error.message);
            }
        });
    }

    // Facebook sign-in
    const facebookSignInBtn = document.getElementById('facebook-signin');
    if (facebookSignInBtn) {
        facebookSignInBtn.addEventListener('click', async function(event) {
            event.preventDefault();
            try {
                const provider = new FacebookAuthProvider();
                const result = await signInWithPopup(auth, provider);
                const firebaseUser = result.user;

                // Sync with SQL database
                const response = await fetch(`${API_BASE_URL}/users/auth`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        firebase_uid: firebaseUser.uid,
                        email: firebaseUser.email,
                        display_name: firebaseUser.displayName,
                        photo_url: firebaseUser.photoURL
                    })
                });

                if (!response.ok) {
                    throw new Error('Failed to sync user with database');
                }

                const userData = await response.json();

                localStorage.setItem('user', JSON.stringify({
                    ...userData,
                    firebase_uid: firebaseUser.uid
                }));
                localStorage.setItem('isLoggedIn', 'true');

                window.location.href = 'account.html';
            } catch (error) {
                console.error('Facebook sign-in error:', error);
                alert('Facebook sign-in failed: ' + error.message);
            }
        });
    }

    // Handle forgot password
    document.getElementById('forgot-password-link') ? .addEventListener('click', async(e) => {
        e.preventDefault();
        const email = document.getElementById('email').value;

        if (!email) {
            errorMessage.textContent = 'Please enter your email address first';
            errorMessage.style.display = 'block';
            return;
        }

        try {
            const response = await fetch('http://localhost:3000/api/auth/forgot-password', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ email })
            });

            if (!response.ok) {
                throw new Error('Failed to send password reset email');
            }

            errorMessage.textContent = 'Password reset email sent. Please check your inbox.';
            errorMessage.style.display = 'block';
            errorMessage.style.backgroundColor = '#d4edda';
            errorMessage.style.color = '#155724';
        } catch (error) {
            console.error('Forgot password error:', error);
            errorMessage.textContent = error.message || 'Failed to send password reset email';
            errorMessage.style.display = 'block';
        }
    });
});

function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 p-4 rounded-md text-white ${
        type === 'error' ? 'bg-red-500' : 'bg-green-500'
    }`;
    notification.textContent = message;
    document.body.appendChild(notification);
    setTimeout(() => notification.remove(), 3000);
}

async function handlePasswordReset(email) {
    try {
        const response = await fetch(`${API_BASE_URL}/auth/reset-password`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ email })
        });

        if (!response.ok) {
            throw new Error('Failed to send password reset email');
        }

        showNotification('Password reset email sent. Please check your inbox.', 'success');
    } catch (error) {
        console.error('Password reset error:', error);
        showNotification(error.message, 'error');
    }
}