<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Your Store</title>
    <link rel="stylesheet" href="../assets/css/main.css">
    <link rel="stylesheet" href="../assets/css/account.css">
</head>

<body>
    <div class="container">
        <div class="auth-container">
            <h1>Login to Your Account</h1>
            <form id="loginForm" class="auth-form">
                <div class="form-group">
                    <label for="email">Email</label>
                    <input type="email" id="email" name="email" required>
                </div>
                <div class="form-group">
                    <label for="password">Password</label>
                    <input type="password" id="password" name="password" required>
                </div>
                <div class="form-group">
                    <label class="checkbox-label">
                        <input type="checkbox" id="rememberMe" name="rememberMe">
                        Remember me
                    </label>
                </div>
                <button type="submit" class="btn btn-primary">Login</button>
                <div class="auth-links">
                    <a href="register.html">Don't have an account? Register</a>
                    <a href="reset-password.html">Forgot password?</a>
                </div>
            </form>
        </div>
    </div>

    <div id="notification" class="notification"></div>

    <script type="module">
        import authService from './js/auth-service.js'; document.getElementById('loginForm').addEventListener('submit', async(e) => { e.preventDefault(); const email = document.getElementById('email').value; const password = document.getElementById('password').value;
        const rememberMe = document.getElementById('rememberMe').checked; try { await authService.login(email, password); window.location.href = '../account/dashboard.html'; } catch (error) { const notification = document.getElementById('notification');
        notification.textContent = error.message; notification.classList.add('error', 'show'); setTimeout(() => { notification.classList.remove('show'); }, 5000); } });
    </script>
</body>

</html>