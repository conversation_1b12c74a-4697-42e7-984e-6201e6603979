"use strict";

import { auth, db } from "./firebase.js";
import {
  doc,
  getDoc,
  updateDoc,
} from "https://www.gstatic.com/firebasejs/11.0.2/firebase-firestore.js";

document.addEventListener("DOMContentLoaded", async () => {
  const user = JSON.parse(localStorage.getItem("user"));
  if (!user) {
    alert("Please log in to access the dashboard.");
    window.location.href = "login.html";
    return;
  }
 
  const userNameElement = document.getElementById("user-name");
  const userEmailElement = document.getElementById("user-email");
  const userPhoneElement = document.getElementById("user-phone");
  const userAddressElement = document.getElementById("user-address");

  try {
    const userDoc = await getDoc(doc(db, "users", user.uid));
    if (userDoc.exists()) {
      const userData = userDoc.data();
      userNameElement.textContent = userData.name || "N/A";
      userEmailElement.textContent = userData.email || "N/A";
      userPhoneElement.textContent = userData.phone || "N/A";
      userAddressElement.textContent = userData.address || "N/A";
    } else {
      alert("User data not found.");
    }
  } catch (error) {
    console.error("Error fetching user data:", error);
    alert("Failed to load user data.");
  }

  document
    .getElementById("update-profile")
    .addEventListener("click", async () => {
      const updatedName = prompt(
        "Enter your new name:",
        userNameElement.textContent
      );
      const updatedPhone = prompt(
        "Enter your new phone number:",
        userPhoneElement.textContent
      );
      const updatedAddress = prompt(
        "Enter your new address:",
        userAddressElement.textContent
      );

      try {
        await updateDoc(doc(db, "users", user.uid), {
          name: updatedName,
          phone: updatedPhone,
          address: updatedAddress,
        });
        alert("Profile updated successfully!");
        userNameElement.textContent = updatedName;
        userPhoneElement.textContent = updatedPhone;
        userAddressElement.textContent = updatedAddress;
      } catch (error) {
        console.error("Error updating profile:", error);
        alert("Failed to update profile.");
      }
    });
});
