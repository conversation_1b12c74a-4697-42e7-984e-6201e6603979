<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!--    
    - primary meta tag
  -->
    <title>Indian Furniture House - Luxury Furniture</title>
    <meta name="title" content="Woodex - Get Quality Furniture">
    <meta name="description" content="Discover luxury furniture pieces for your home at Indian Furniture House">

    <!--   
    - favicon
  -->
    <link rel="shortcut icon" href="./favicon.svg" type="image/svg+xml">

    <!--  
    - custom css link
  -->
    <link rel="stylesheet" href="./assets/css/style.css">

    <!-- 
    - google font link
  -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- 
    - preload images
  -->
    <link rel="preload" as="image" href="./assets/images/hero-product-1.jpg">
    <link rel="preload" as="image" href="./assets/images/hero-product-2.jpg">
    <link rel="preload" as="image" href="./assets/images/hero-product-3.jpg">
    <link rel="preload" as="image" href="./assets/images/hero-product-4.jpg">
    <link rel="preload" as="image" href="./assets/images/hero-product-5.jpg">
    <script type="module" src="login.js" defer></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const isLoggedIn = localStorage.getItem('isLoggedIn') === 'true';
            if (!isLoggedIn) {
                window.location.href = 'login.html';
            }
        });
    </script>

    <style>
         :root {
            --primary-color: #1a1a1a;
            --secondary-color: #ff7f50;
            --text-color: #333;
            --light-text: #666;
            --background-light: #f4f4f4;
            --font-heading: 'Playfair Display', serif;
            --font-body: 'Poppins', sans-serif;
        }
        
        body {
            font-family: var(--font-body);
            color: var(--text-color);
            line-height: 1.6;
        }
        /* Hero Section */
        
        .hero-section {
            position: relative;
            height: 100vh;
            background: linear-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.3)), url('https://images.unsplash.com/photo-1618221195710-dd6b41faaea6?q=80&w=2000&auto=format&fit=crop') center/cover no-repeat;
            display: flex;
            align-items: center;
            padding: 0 5%;
        }
        
        .hero-content {
            max-width: 800px;
            color: rgb(255, 255, 255);
            animation: fadeInUp 1s ease;
        }
        
        .hero-subtitle {
            font-family: var(--font-body);
            font-size: 1.2rem;
            text-transform: uppercase;
            letter-spacing: 3px;
            margin-bottom: 1rem;
        }
        
        .hero-title {
            font-family: var(--font-heading);
            font-size: 4.5rem;
            line-height: 1.2;
            margin-bottom: 1.5rem;
        }
        
        .hero-description {
            font-size: 1.1rem;
            margin-bottom: 2rem;
            max-width: 600px;
        }
        
        .hero-btn {
            display: inline-block;
            padding: 1rem 2.5rem;
            background: var(--secondary-color);
            color: white;
            text-decoration: none;
            border-radius: 30px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .hero-btn:hover {
            background: #ff6347;
            transform: translateY(-2px);
        }
        /* Features Section */
        
        .features-section {
            padding: 4rem 5%;
            background: white;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 2rem;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .feature-item {
            text-align: center;
            padding: 2rem;
            background: var(--background-light);
            border-radius: 10px;
            transition: transform 0.3s ease;
        }
        
        .feature-item:hover {
            transform: translateY(-10px);
        }
        
        .feature-icon {
            font-size: 2.5rem;
            color: var(--secondary-color);
            margin-bottom: 1rem;
        }
        
        .feature-title {
            font-family: var(--font-heading);
            font-size: 1.2rem;
            margin-bottom: 0.5rem;
        }
        
        .feature-text {
            color: var(--light-text);
            font-size: 0.9rem;
        }
        /* Popular Products Section */
        
        .popular-products {
            padding: 4rem 5%;
            background: var(--background-light);
        }
        
        .section-header {
            text-align: center;
            margin-bottom: 3rem;
        }
        
        .section-subtitle {
            color: var(--secondary-color);
            font-size: 1.1rem;
            text-transform: uppercase;
            letter-spacing: 2px;
            margin-bottom: 0.5rem;
        }
        
        .section-title {
            font-family: var(--font-heading);
            font-size: 2.5rem;
            color: var(--primary-color);
            margin-bottom: 1rem;
        }
        
        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .product-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }
        
        .product-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }
        
        .product-image {
            position: relative;
            padding-top: 75%;
            overflow: hidden;
        }
        
        .product-image img {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.5s ease;
        }
        
        .product-card:hover .product-image img {
            transform: scale(1.1);
        }
        
        .product-content {
            padding: 1.5rem;
        }
        
        .product-category {
            font-size: 0.9rem;
            color: var(--secondary-color);
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 0.5rem;
        }
        
        .product-title {
            font-family: var(--font-heading);
            font-size: 1.2rem;
            margin-bottom: 0.5rem;
            color: var(--primary-color);
        }
        
        .product-price {
            font-size: 1.3rem;
            color: var(--secondary-color);
            font-weight: 600;
        }
        
        .product-actions {
            display: flex;
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .action-btn {
            flex: 1;
            padding: 0.8rem;
            border: none;
            border-radius: 5px;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .add-to-cart {
            background: var(--secondary-color);
            color: white;
        }
        
        .add-to-cart:hover {
            background: #ff6347;
        }
        
        .add-to-wishlist {
            background: var(--background-light);
            color: var(--primary-color);
        }
        
        .add-to-wishlist:hover {
            background: #eee;
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @media (max-width: 768px) {
            .hero-title {
                font-size: 3rem;
            }
            .features-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            .products-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
        
        @media (max-width: 480px) {
            .hero-title {
                font-size: 2.5rem;
            }
            .features-grid {
                grid-template-columns: 1fr;
            }
            .products-grid {
                grid-template-columns: 1fr;
            }
        }
        /* Import Google Fonts */
        
        @import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@600;700&family=Montserrat:wght@300;400;500;600;700&display=swap');
        .brand-container {
            text-decoration: none;
            display: flex;
            align-items: center;
            padding: 0.5rem;
            position: relative;
        }
        
        .logo-modern {
            display: flex;
            align-items: center;
            gap: 1.2rem;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
            padding: 0.8rem 1.8rem;
            border-radius: 50px;
            position: relative;
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
        }
        
        .sofa-icon {
            position: relative;
            width: 42px;
            height: 42px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .sofa-icon i {
            font-size: 1.8rem;
            color: #ff7f50;
            position: relative;
            z-index: 1;
            filter: drop-shadow(0 2px 4px rgba(255, 127, 80, 0.3));
            animation: float 3s ease-in-out infinite;
        }
        
        .icon-glow {
            position: absolute;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle, rgba(255, 127, 80, 0.2) 0%, transparent 70%);
            animation: pulse 2s ease-in-out infinite;
        }
        
        .brand-text {
            position: relative;
            overflow: hidden;
        }
        
        .text-full {
            font-family: 'Playfair Display', serif;
            font-size: 1.7rem;
            font-weight: 700;
            background: linear-gradient(135deg, #ff7f50, #ff6347);
            background-clip: text;
            -webkit-background-clip: text;
            color: transparent;
            -webkit-text-fill-color: transparent;
            letter-spacing: 1.5px;
            position: relative;
            z-index: 1;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
            text-transform: uppercase;
            font-feature-settings: "lnum";
            font-variant-numeric: lining-nums;
        }
        
        .text-shine {
            position: absolute;
            top: 0;
            left: -100%;
            width: 50%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            animation: shine 3s infinite;
        }
        
        @keyframes float {
            0%,
            100% {
                transform: translateY(0);
            }
            50% {
                transform: translateY(-3px);
            }
        }
        
        @keyframes pulse {
            0%,
            100% {
                transform: scale(1);
                opacity: 0.5;
            }
            50% {
                transform: scale(1.2);
                opacity: 0.8;
            }
        }
        
        @keyframes shine {
            0% {
                left: -100%;
            }
            100% {
                left: 200%;
            }
        }
        /* Hover Effects */
        
        .logo-modern:hover {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05));
            transform: translateY(-1px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;
        }
        
        .logo-modern:hover .sofa-icon i {
            color: #ff6347;
            transform: scale(1.1);
            transition: all 0.3s ease;
        }
        
        .logo-modern:hover .text-full {
            background: linear-gradient(135deg, #ff6347, #ff4500);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            transition: all 0.3s ease;
        }
        /* Glass Effect */
        
        .logo-modern::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient( 135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
            backdrop-filter: blur(10px);
            z-index: 0;
        }
        /* Header Actions */
        
        .header-action {
            display: flex;
            gap: 1.2rem;
            align-items: center;
        }
        
        .header-action-btn {
            font-family: 'Montserrat', sans-serif;
            font-weight: 500;
        }
        
        @media (max-width: 768px) {
            .logo-modern {
                padding: 0.6rem 1.2rem;
            }
            .sofa-icon {
                width: 36px;
                height: 36px;
            }
            .sofa-icon i {
                font-size: 1.4rem;
            }
            .text-full {
                font-size: 1.3rem;
                letter-spacing: 1px;
            }
        }
        
        @media (max-width: 480px) {
            .text-full {
                font-size: 1.1rem;
                letter-spacing: 0.5px;
            }
            .sofa-icon {
                width: 32px;
                height: 32px;
            }
        }
        /* Header Layout */
        
        .header-main {
            display: flex;
            align-items: center;
            gap: 2rem;
            width: 100%;
        }
        /* Search Container Styles */
        
        .search-container {
            flex: 1;
            max-width: 600px;
            position: relative;
        }
        
        .search-wrapper {
            display: flex;
            align-items: center;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 50px;
            padding: 0.5rem;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }
        
        .search-wrapper:focus-within {
            background: rgba(255, 255, 255, 0.15);
            border-color: rgba(255, 255, 255, 0.3);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        
        .search-input {
            flex: 1;
            background: transparent;
            border: none;
            padding: 0.5rem 1rem;
            color: #333;
            font-family: 'Montserrat', sans-serif;
            font-size: 0.95rem;
            outline: none;
        }
        
        .search-input::placeholder {
            color: #666;
        }
        
        .search-btn {
            background: none;
            border: none;
            color: #666;
            padding: 0.5rem;
            cursor: pointer;
            transition: color 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .search-btn:hover {
            color: #ff7f50;
        }
        
        .search-btn ion-icon {
            font-size: 1.2rem;
        }
        /* Suggestions Container */
        
        .suggestions-container {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            margin-top: 0.5rem;
            display: none;
            z-index: 1000;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .suggestion-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 0.75rem 1rem;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }
        
        .suggestion-item:hover {
            background-color: #f8f9fa;
        }
        
        .suggestion-image {
            width: 50px;
            height: 50px;
            object-fit: cover;
            border-radius: 8px;
        }
        
        .suggestion-details {
            flex: 1;
        }
        
        .suggestion-name {
            font-weight: 500;
            color: #333;
            margin-bottom: 0.25rem;
            display: block;
        }
        
        .suggestion-price {
            color: #ff7f50;
            font-weight: 600;
            font-size: 0.9rem;
        }
        
        @media (max-width: 1024px) {
            .header-main {
                flex-wrap: wrap;
            }
            .search-container {
                order: 3;
                width: 100%;
                max-width: none;
                margin-top: 1rem;
            }
        }
        
        @media (max-width: 768px) {
            .header-main {
                gap: 1rem;
            }
            .search-input {
                font-size: 0.9rem;
            }
        }
        
        .header {
            background: white;
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            position: fixed;
            width: 100%;
            top: 0;
            left: 0;
            z-index: 1000;
        }
        
        .header-main {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 2rem;
        }
        
        .brand-container {
            text-decoration: none;
            min-width: fit-content;
        }
        
        .brand-text {
            font-family: 'Montserrat', sans-serif;
            font-size: 1.5rem;
            font-weight: 600;
            color: #ff7f50;
            letter-spacing: 0.5px;
        }
        
        .search-container {
            flex: 1;
            max-width: 600px;
        }
        
        .search-wrapper {
            display: flex;
            align-items: center;
            background: #f5f5f5;
            border: 1px solid #eee;
            border-radius: 50px;
            padding: 0.5rem 1rem;
            transition: all 0.3s ease;
        }
        
        .search-wrapper:focus-within {
            background: white;
            border-color: #ff7f50;
            box-shadow: 0 0 0 2px rgba(255, 127, 80, 0.1);
        }
        
        .search-input {
            flex: 1;
            background: transparent;
            border: none;
            padding: 0.5rem;
            font-family: 'Montserrat', sans-serif;
            font-size: 0.95rem;
            color: #333;
            outline: none;
        }
        
        .search-input::placeholder {
            color: #999;
        }
        
        .search-btn {
            background: none;
            border: none;
            color: #999;
            padding: 0.5rem;
            cursor: pointer;
            transition: color 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .search-btn:hover {
            color: #ff7f50;
        }
        
        .header-action {
            display: flex;
            align-items: center;
            gap: 1.5rem;
        }
        
        .header-action-btn {
            position: relative;
            color: #333;
            font-size: 1.4rem;
            transition: color 0.3s ease;
        }
        
        .header-action-btn:hover {
            color: #ff7f50;
        }
        
        .btn-badge {
            position: absolute;
            top: -8px;
            right: -8px;
            background: #ff7f50;
            color: white;
            font-size: 0.7rem;
            font-weight: 600;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        @media (max-width: 1024px) {
            .header-main {
                flex-wrap: wrap;
                gap: 1rem;
            }
            .search-container {
                order: 3;
                width: 100%;
                max-width: none;
            }
        }
        
        @media (max-width: 768px) {
            .brand-text {
                font-size: 1.2rem;
            }
            .header-action {
                gap: 1rem;
            }
            .header-action-btn {
                font-size: 1.2rem;
            }
            .search-wrapper {
                padding: 0.6rem 1rem;
            }
        }
        
        .logout-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            background: linear-gradient(135deg, #ff7f50, #ff6347);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            border: none;
            cursor: pointer;
            font-family: var(--font-body);
            font-weight: 500;
            transition: all 0.3s ease;
            margin-left: 1rem;
        }
        
        .logout-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 127, 80, 0.3);
            background: linear-gradient(135deg, #ff6347, #ff4500);
        }
        
        .logout-btn ion-icon {
            font-size: 1.2rem;
        }
        
        .logout-text {
            font-size: 0.9rem;
        }
        
        @media (max-width: 768px) {
            .logout-text {
                display: none;
            }
            .logout-btn {
                padding: 0.5rem;
            }
        }
        
        #notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 1rem 2rem;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            z-index: 1000;
            display: none;
            animation: slideIn 0.3s ease;
        }
        
        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
        /* Modal Styles */
        
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            z-index: 1000;
            backdrop-filter: blur(5px);
        }
        
        .modal-content {
            position: relative;
            background: var(--bg-dark);
            width: 90%;
            max-width: 800px;
            margin: 2rem auto;
            border-radius: 20px;
            padding: 2rem;
            animation: modalSlideIn 0.3s ease-out;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--card-border);
        }
        
        .modal-header h2 {
            color: var(--text-light);
            font-size: 1.8rem;
        }
        
        .close-btn {
            background: none;
            border: none;
            color: var(--text-gray);
            font-size: 1.5rem;
            cursor: pointer;
            transition: color 0.3s ease;
        }
        
        .close-btn:hover {
            color: var(--primary);
        }
        
        .product-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
        }
        
        .product-image {
            width: 100%;
            border-radius: 15px;
            overflow: hidden;
        }
        
        .product-image img {
            width: 100%;
            height: auto;
            object-fit: cover;
            transition: transform 0.3s ease;
        }
        
        .product-image:hover img {
            transform: scale(1.05);
        }
        
        .product-info {
            color: var(--text-light);
        }
        
        .product-info h3 {
            font-size: 1.8rem;
            margin-bottom: 1rem;
            color: var(--text-light);
        }
        
        .product-price {
            font-size: 1.5rem;
            color: var(--primary);
            margin-bottom: 1.5rem;
        }
        
        .product-description {
            color: var(--text-gray);
            margin-bottom: 2rem;
            line-height: 1.6;
        }
        
        .product-actions {
            display: flex;
            gap: 1rem;
        }
        
        .action-button {
            flex: 1;
            padding: 1rem;
            border: none;
            border-radius: 10px;
            font-size: 1rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
        }
        
        .add-to-cart {
            background: var(--primary);
            color: white;
        }
        
        .add-to-cart:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        
        .remove-favorite {
            background: rgba(220, 38, 38, 0.1);
            color: #dc2626;
            border: 1px solid rgba(220, 38, 38, 0.2);
        }
        
        .remove-favorite:hover {
            background: rgba(220, 38, 38, 0.2);
            transform: translateY(-2px);
        }
        
        @keyframes modalSlideIn {
            from {
                transform: translateY(-50px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }
        
        @media (max-width: 768px) {
            .modal-content {
                margin: 1rem;
                padding: 1rem;
            }
            .product-details {
                grid-template-columns: 1fr;
            }
        }
    </style>
    <script type="module" src="https://www.gstatic.com/firebasejs/9.6.1/firebase-app.js"></script>
    <script type="module" src="https://www.gstatic.com/firebasejs/9.6.1/firebase-auth.js"></script>
    <script type="module" src="https://www.gstatic.com/firebasejs/9.6.1/firebase-firestore.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Header Styles */
        
        .header {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            padding: 1rem 0;
        }
        
        .header .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 2rem;
        }
        /* Brand Section */
        
        .brand-logo {
            display: flex;
            align-items: center;
            gap: 1rem;
            text-decoration: none;
            color: var(--text-light);
        }
        
        .brand-logo i {
            font-size: 2rem;
            color: var(--primary);
            filter: drop-shadow(0 0 10px rgba(255, 127, 80, 0.3));
        }
        
        .brand-text {
            font-size: 1.5rem;
            font-weight: 600;
            background: linear-gradient(45deg, #fff, var(--primary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        /* Search Section */
        
        .search-section {
            flex: 1;
            max-width: 600px;
        }
        
        .search-bar {
            display: flex;
            align-items: center;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50px;
            padding: 0.5rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .search-input {
            flex: 1;
            background: none;
            border: none;
            padding: 0.5rem 1rem;
            color: var(--text-light);
            font-size: 1rem;
        }
        
        .search-input::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }
        
        .search-input:focus {
            outline: none;
        }
        
        .search-btn {
            background: var(--primary);
            color: white;
            border: none;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .search-btn:hover {
            background: var(--primary-dark);
            transform: scale(1.05);
        }
        /* Navigation Section */
        
        .nav-section {
            display: flex;
            gap: 2rem;
        }
        
        .nav-link {
            color: var(--text-gray);
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .nav-link:hover,
        .nav-link.active {
            color: var(--text-light);
        }
        
        .nav-link::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 0;
            height: 2px;
            background: var(--primary);
            transition: width 0.3s ease;
        }
        
        .nav-link:hover::after,
        .nav-link.active::after {
            width: 100%;
        }
        /* User Actions Section */
        
        .user-actions {
            display: flex;
            align-items: center;
            gap: 1.5rem;
        }
        
        .action-btn {
            position: relative;
            color: var(--text-gray);
            text-decoration: none;
            font-size: 1.2rem;
            transition: all 0.3s ease;
        }
        
        .action-btn:hover {
            color: var(--text-light);
        }
        
        .badge {
            position: absolute;
            top: -8px;
            right: -8px;
            background: var(--primary);
            color: white;
            font-size: 0.7rem;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        /* User Profile Section */
        
        .user-profile {
            position: relative;
        }
        
        .profile-btn {
            display: flex;
            align-items: center;
            gap: 0.8rem;
            background: none;
            border: none;
            color: var(--text-light);
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 50px;
            transition: all 0.3s ease;
        }
        
        .profile-btn:hover {
            background: rgba(255, 255, 255, 0.1);
        }
        
        .profile-btn img {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            border: 2px solid var(--primary);
        }
        
        .profile-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            width: 220px;
            background: rgba(30, 30, 30, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 1rem;
            margin-top: 1rem;
            display: none;
            transform-origin: top right;
            animation: dropdownShow 0.3s ease;
        }
        
        .profile-dropdown.show {
            display: block;
        }
        
        .dropdown-item {
            display: flex;
            align-items: center;
            gap: 0.8rem;
            padding: 0.8rem;
            color: var(--text-gray);
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .dropdown-item:hover {
            background: rgba(255, 255, 255, 0.1);
            color: var(--text-light);
        }
        
        .dropdown-item i {
            font-size: 1.1rem;
            width: 20px;
            text-align: center;
        }
        
        .logout-btn {
            width: 100%;
            background: none;
            border: none;
            font-size: 1rem;
            cursor: pointer;
            color: #ff4444;
        }
        
        .logout-btn:hover {
            background: rgba(255, 68, 68, 0.1);
        }
        
        @keyframes dropdownShow {
            from {
                opacity: 0;
                transform: scale(0.9);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }
        /* Responsive Design */
        
        @media (max-width: 1200px) {
            .nav-section {
                display: none;
            }
        }
        
        @media (max-width: 768px) {
            .header .container {
                padding: 0 1rem;
            }
            .brand-text {
                display: none;
            }
            .search-section {
                display: none;
            }
            .user-actions {
                gap: 1rem;
            }
            .username {
                display: none;
            }
        }
        /* Notification Styles */
        
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 1rem 2rem;
            border-radius: 8px;
            color: white;
            z-index: 1001;
            animation: slideIn 0.3s ease-out;
        }
        
        .notification.success {
            background: rgba(46, 213, 115, 0.95);
        }
        
        .notification.error {
            background: rgba(255, 71, 87, 0.95);
        }
        
        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
    </style>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Profile Dropdown Toggle
            const profileBtn = document.getElementById('profileBtn');
            const profileDropdown = document.getElementById('profileDropdown');
            const userProfileSection = document.getElementById('userProfileSection');

            // Toggle dropdown
            profileBtn.addEventListener('click', function(e) {
                e.stopPropagation();
                profileDropdown.classList.toggle('show');
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (!userProfileSection.contains(e.target)) {
                    profileDropdown.classList.remove('show');
                }
            });

            // Update user info if logged in
            const user = JSON.parse(localStorage.getItem('user'));
            if (user) {
                document.getElementById('username').textContent = user.full_name || 'User';
                if (user.full_name) {
                    document.getElementById('userAvatar').src = `https://ui-avatars.com/api/?name=${encodeURIComponent(user.full_name)}&background=ff7f50&color=fff`;
                }
            }

            // Update cart and favorites count
            updateCounts();
        });

        // Update cart and favorites count
        function updateCounts() {
            const cart = JSON.parse(localStorage.getItem('cart')) || [];
            const favorites = JSON.parse(localStorage.getItem('favorites')) || [];

            document.querySelector('.cart-count').textContent = cart.length;
            document.querySelector('.favorites-count').textContent = favorites.length;
        }

        // Logout function
        function handleLogout() {
            try {
                // Clear user data
                localStorage.removeItem('user');
                localStorage.setItem('isLoggedIn', 'false');

                // Show success message
                const notification = document.createElement('div');
                notification.className = 'notification success';
                notification.textContent = 'Logged out successfully';
                document.body.appendChild(notification);

                // Remove notification after 3 seconds
                setTimeout(() => {
                    notification.remove();
                    // Redirect to login page
                    window.location.href = 'login.html';
                }, 2000);
            } catch (error) {
                console.error('Logout error:', error);

                // Show error message
                const notification = document.createElement('div');
                notification.className = 'notification error';
                notification.textContent = 'Error during logout. Please try again.';
                document.body.appendChild(notification);

                // Remove notification after 3 seconds
                setTimeout(() => notification.remove(), 3000);
            }
        }
    </script>
</head>

<body>
    <div id="header-placeholder"></div>
    <script src="./assets/js/header.js"></script>
    <div id="notification"></div>

    <main>
        <article>

            <!-- 
        - #HERO
      -->

            <section class="hero-section">
                <div class="hero-content">
                    <p class="hero-subtitle">Welcome to Indian Furniture House</p>
                    <h1 class="hero-title">Discover Timeless Elegance in Every Piece</h1>
                    <p class="hero-description">Transform your living space with our exquisite collection of handcrafted furniture, designed to bring luxury and comfort to your home.</p>
                    <a href="#products" class="hero-btn">Explore Collection</a>
                </div>
            </section>

            <!-- Categories Showcase Section -->
            <section class="categories-showcase">
                <div class="container">
                    <h2 class="section-title">Shop by Category</h2>
                    <div class="categories-grid">
                        <div class="category-card furniture">
                            <div class="category-content">
                                <h3>Furniture</h3>
                                <p>Timeless pieces for your home</p>
                                <a href="#product" class="category-btn" data-filter="furniture">Explore</a>
                            </div>
                        </div>
                        <div class="category-card decoration">
                            <div class="category-content">
                                <h3>Decoration</h3>
                                <p>Add style to your space</p>
                                <a href="#product" class="category-btn" data-filter="decoration">Explore</a>
                            </div>
                        </div>
                        <div class="category-card accessory">
                            <div class="category-content">
                                <h3>Accessories</h3>
                                <p>Perfect finishing touches</p>
                                <a href="#product" class="category-btn" data-filter="accessory">Explore</a>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Value Proposition Section -->
            <section class="value-props">
                <div class="container">
                    <div class="value-grid">
                        <div class="value-item">
                            <ion-icon name="shield-checkmark-outline"></ion-icon>
                            <h3>Quality Guaranteed</h3>
                            <p>Every piece is crafted with premium materials and expert craftsmanship</p>
                        </div>
                        <div class="value-item">
                            <ion-icon name="car-outline"></ion-icon>
                            <h3>Free Shipping</h3>
                            <p>Enjoy free delivery on all orders above ₹5000</p>
                        </div>
                        <div class="value-item">
                            <ion-icon name="sync-outline"></ion-icon>
                            <h3>Easy Returns</h3>
                            <p>7-day hassle-free return policy</p>
                        </div>
                        <div class="value-item">
                            <ion-icon name="wallet-outline"></ion-icon>
                            <h3>Secure Payment</h3>
                            <p>Multiple secure payment options available</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Featured Collection -->
            <section class="featured-collection">
                <div class="container">
                    <div class="collection-header">
                        <h2 class="section-title">New Arrivals</h2>
                        <p class="section-subtitle">Discover our latest additions</p>
                    </div>
                    <div class="collection-grid">
                        <div class="collection-main">
                            <img src="./assets/images/new images/modern sofa.png" alt="Modern Sofa">
                            <div class="collection-content">
                                <h3>Modern Living</h3>
                                <p>Contemporary designs for modern homes</p>
                                <a href="#product" class="collection-btn">Shop Now</a>
                            </div>
                        </div>
                        <div class="collection-side">
                            <div class="collection-item">
                                <img src="./assets/images/new images/chair.webp" alt="Designer Chair">
                                <div class="collection-content">
                                    <h4>Designer Chairs</h4>
                                    <a href="#product" class="collection-btn">Explore</a>
                                </div>
                            </div>
                            <div class="collection-item">
                                <img src="./assets/images/new images/mirror.webp" alt="Decorative Mirror">
                                <div class="collection-content">
                                    <h4>Wall Decor</h4>
                                    <a href="#product" class="collection-btn">Explore</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Design Inspiration Section -->
            <section class="design-inspiration">
                <div class="container">
                    <div class="inspiration-content">
                        <h2 class="section-title">Design Inspiration</h2>
                        <p class="section-subtitle">Transform your space with our curated collections</p>
                        <div class="inspiration-grid">
                            <div class="inspiration-item">
                                <img src="./assets/images/new images/Wooden Tool .webp" alt="Living Room">
                                <h3>Living Room</h3>
                                <p>Create a cozy and inviting atmosphere</p>
                            </div>
                            <div class="inspiration-item">
                                <img src="./assets/images/new images/berggrune.webp" alt="Bedroom">
                                <h3>Bedroom</h3>
                                <p>Design your perfect sanctuary</p>
                            </div>
                            <div class="inspiration-item">
                                <img src="./assets/images/new images/Small Tool.webp" alt="Home Office">
                                <h3>Home Office</h3>
                                <p>Stylish and functional workspace solutions</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Trending Products Section -->
            <section class="trending-section">
                <div class="section-header">
                    <h2 class="section-title">Trending Products</h2>
                    <p class="section-subtitle">Don't miss out on these hot deals!</p>
                    <section class="countdown-section">
                        <h2 class="countdown-title">Special Offer Ends In</h2>
                        <div class="countdown-digits">
                            <!-- Countdown will be populated by JavaScript -->
                        </div>
                    </section>
                    <div class="countdown-section">
                        <div class="countdown-timer">
                            <div class="countdown-item">
                                <div class="countdown-number" id="days">00</div>
                                <div class="countdown-label">Days</div>
                            </div>
                            <div class="countdown-item">
                                <div class="countdown-number" id="hours">00</div>
                                <div class="countdown-label">Hours</div>
                            </div>
                            <div class="countdown-item">
                                <div class="countdown-number" id="minutes">00</div>
                                <div class="countdown-label">Minutes</div>
                            </div>
                            <div class="countdown-item">
                                <div class="countdown-number" id="seconds">00</div>
                                <div class="countdown-label">Seconds</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="trending-grid" id="trending-products">
                    <!-- Trending products will be dynamically loaded here -->
                </div>
            </section>

            <!-- Features Section -->
            <section class="features-section">
                <div class="features-grid">
                    <div class="feature-item">
                        <div class="feature-icon">
                            <ion-icon name="cube-outline"></ion-icon>
                        </div>
                        <h3 class="feature-title">Premium Quality</h3>
                        <p class="feature-text">Handcrafted with the finest materials for lasting beauty</p>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">
                            <ion-icon name="car-outline"></ion-icon>
                        </div>
                        <h3 class="feature-title">Free Shipping</h3>
                        <p class="feature-text">Free delivery on all orders above ₹5000</p>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">
                            <ion-icon name="shield-checkmark-outline"></ion-icon>
                        </div>
                        <h3 class="feature-title">10 Year Warranty</h3>
                        <p class="feature-text">Quality guaranteed with our extensive warranty</p>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">
                            <ion-icon name="headset-outline"></ion-icon>
                        </div>
                        <h3 class="feature-title">24/7 Support</h3>
                        <p class="feature-text">Expert assistance available around the clock</p>
                    </div>
                </div>
            </section>

            <!-- Add the countdown timer section before the product section -->


            <!-- Keep only one product section with filter -->
            <section class="product" id="product">
                <div class="container">
                    <div class="title-wrapper">
                        <h2 class="h2 section-title">Our Products</h2>

                        <div class="filter-btn-list">
                            <button class="filter-btn active" data-filter="all">All</button>
                            <button class="filter-btn" data-filter="decoration">Decoration</button>
                            <button class="filter-btn" data-filter="furniture">Furniture</button>
                            <button class="filter-btn" data-filter="accessory">Accessories</button>
                        </div>
                    </div>

                    <ul class="grid-list product-list" id="product-list">
                        <!-- Products will be populated by JavaScript -->
                    </ul>
                </div>
            </section>

            <!-- 
        - #ABOUT
      -->

            <section class="section about" id="about" aria-label="about">
                <div class="container">

                    <h2 class="section-title">Indian Furniture Homes </h2>

                    <p class="section-text">
                        When you start with a portrait and search for a pure form, a clear volume, through successive eliminations, you arrive inevitably at the egg. Likewise, starting with the egg and following the same process in reverse, one finishes with the portrait.
                    </p>

                    <div class="about-card">
                        <div class="video-container">
                            <video id="promo-video" src="assets/WhatsApp Video 2025-02-19 at 00.30.27_deed9758.mp4" width="100%" height="100%" loading="lazy" class="img-cover">
                  <track kind="subtitles" src="assets/subtitles.vtt" srclang="en" label="English">
                  <track kind="descriptions" src="assets/descriptions.vtt" srclang="en" label="English">
                  <p>Your browser doesn't support HTML5 video. Here is a <a href="assets/WhatsApp Video 2025-02-19 at 00.30.27_deed9758.mp4">link to the video</a> instead.</p>
                </video>
                        </div>

                        <button id="play-btn" class="play-btn" aria-label="play video" onclick="toggleVideo()">
                <ion-icon name="play-circle-outline" aria-hidden="true"></ion-icon>
            </button>
                    </div>

                    <script>
                        function toggleVideo() {
                            let video = document.getElementById("promo-video");
                            let playBtn = document.getElementById("play-btn");

                            if (video.paused) {
                                video.play();
                                playBtn.style.display = "none"; // Hide play button when playing
                            }
                        }

                        // Show play button when video is paused or ended
                        document.getElementById("promo-video").addEventListener("pause", function() {
                            document.getElementById("play-btn").style.display = "block";
                        });

                        document.getElementById("promo-video").addEventListener("ended", function() {
                            document.getElementById("play-btn").style.display = "block";
                        });
                    </script>


                </div>
            </section>

            <!-- 
        - #NEWSLETTER
      -->

            <section class="section newsletter" aria-label="newsletter">
                <div class="container">
                    <div class="newsletter-card">
                        <div class="card-content">
                            <h2 class="h2 section-title">Join Our Newsletter</h2>
                            <p class="section-text">
                                Subscribe to get exclusive offers, new arrivals, and special discounts. Plus, get 15% off your first order!
                            </p>
                            <div class="newsletter-benefits">
                                <div class="benefit-item">
                                    <ion-icon name="gift-outline"></ion-icon>
                                    <span>Exclusive Offers</span>
                                </div>
                                <div class="benefit-item">
                                    <ion-icon name="notifications-outline"></ion-icon>
                                    <span>New Arrivals</span>
                                </div>
                                <div class="benefit-item">
                                    <ion-icon name="star-outline"></ion-icon>
                                    <span>Special Discounts</span>
                                </div>
                            </div>
                        </div>

                        <form action="" class="card-form">
                            <div class="input-wrapper">
                                <input type="email" name="email_address" placeholder="Enter your email address" required class="email-field">
                                <button type="submit" class="newsletter-btn" aria-label="subscribe">
                  <span>Subscribe</span>
                <ion-icon name="arrow-forward" aria-hidden="true"></ion-icon>
              </button>
                            </div>
                            <p class="newsletter-note">By subscribing, you agree to our Privacy Policy and consent to receive updates from our company.</p>
                        </form>
                    </div>
                </div>
            </section>

        </article>
    </main>

    <!-- 
    - #FOOTER
  -->

    <footer class="footer">
        <div class="container">

            <div class="footer-top section">

                <div class="footer-brand">

                    <a href="login.html" class="logo">INDIAN FURNITURE HOUSE</a>

                    <ul>

                        <li class="footer-list-item">
                            <ion-icon name="location-sharp" aria-hidden="true"></ion-icon>

                            <address class="address">
                01,MAIN ROAD ,NEW DELHI,INDIA
              </address>
                        </li>

                        <li class="footer-list-item">
                            <ion-icon name="call-sharp" aria-hidden="true"></ion-icon>

                            <a href="#" class="footer-link">+1234567890</a>
                        </li>

                        <li>
                            <ul class="social-list">

                                <li>
                                    <a href="https://www.facebook.com/" class="social-link">
                                        <ion-icon name="logo-facebook"></ion-icon>
                                    </a>
                                </li>

                                <li>
                                    <a href="https://x.com/" class="social-link">
                                        <ion-icon name="logo-twitter"></ion-icon>
                                    </a>
                                </li>

                                <li>
                                    <a href="#" class="social-link">
                                        <ion-icon name="logo-tumblr"></ion-icon>
                                    </a>
                                </li>

                            </ul>
                        </li>

                    </ul>

                </div>

                <ul class="footer-list">

                    <li>
                        <p class="footer-list-title">Help & Information</p>
                    </li>

                    <li>
                        <a href="#" class="footer-link">Help & Contact Us</a>
                    </li>

                    <li>
                        <a href="#" class="footer-link">Returns & Refunds</a>
                    </li>

                    <li>
                        <a href="#" class="footer-link">Online Stores</a>
                    </li>

                    <li>
                        <a href="#" class="footer-link">Terms & Conditions</a>
                    </li>

                </ul>

                <ul class="footer-list">

                    <li>
                        <p class="footer-list-title">About Us</p>
                    </li>

                    <li>
                        <a href="#" class="footer-link">About Us</a>
                    </li>

                    <li>
                        <a href="#" class="footer-link">What We Do</a>
                    </li>

                    <li>
                        <a href="#" class="footer-link">FAQ Page</a>
                    </li>

                    <li>
                        <a href="#" class="footer-link">Contact Us</a>
                    </li>

                </ul>

                <div class="footer-list">

                    <p class="footer-list-title">Newsletter</p>

                    <form action="" class="footer-form">
                        <input type="email" name="email_address" placeholder="Your email address" required class="email-field">

                        <button type="submit" class="footer-form-btn">
              <ion-icon name="arrow-forward" aria-hidden="true"></ion-icon>
            </button>
                    </form>

                    <div class="wrapper">

                        <a href="#" class="footer-link">Term & Condition</a>
                        <a href="#" class="footer-link">Policy</a>
                        <a href="#" class="footer-link">Map</a>

                    </div>

                </div>

            </div>

            <div class="footer-bottom">



            </div>

        </div>
    </footer>

    <!-- 
    - #BACK TO TOP
  -->

    <a href="#top" class="back-top-btn" aria-label="back to top" data-back-top-btn>
        <ion-icon name="arrow-up" aria-hidden="true"></ion-icon>
    </a>

    <!-- 
    - custom js link
  -->
    <script src="./assets/js/products.js"></script>
    <script src="./assets/js/script.js"></script>
    <script type="module" src="https://unpkg.com/ionicons@5.5.2/dist/ionicons/ionicons.esm.js"></script>
    <script nomodule src="https://unpkg.com/ionicons@5.5.2/dist/ionicons/ionicons.js"></script>

    <!-- Quick View Modal -->
    <div id="quick-view-modal" class="quick-view-modal">
        <div class="modal-content">
            <button id="close-quick-view" class="close-modal">
        <ion-icon name="close-outline"></ion-icon>
      </button>
            <div class="quick-view-content">
                <!-- Content will be dynamically loaded here -->
            </div>
        </div>
    </div>

    <!-- Chatbot Widget -->
    <div class="chatbot-widget" id="chatbot-widget">
        <button class="chatbot-toggle" id="chatbot-toggle">
            <ion-icon name="chatbubbles-outline"></ion-icon>
            <span class="chatbot-badge">1</span>
    </button>

        <div class="chatbot-container">
            <div class="chatbot-header">
                <h3>Customer Support</h3>
                <div class="chatbot-actions">
                    <button id="chatbot-minimize" class="chatbot-minimize">
                        <ion-icon name="remove-outline"></ion-icon>
                    </button>
                    <button id="chatbot-close" class="chatbot-close">
          <ion-icon name="close-outline"></ion-icon>
        </button>
                </div>
            </div>

            <div class="chatbot-body">
                <div class="chat-messages" id="chat-messages">
                    <!-- Messages will be added here dynamically -->
                </div>

                <div class="quick-actions">
                    <button class="quick-action-btn" onclick="handleQuickAction('products')">Browse Products</button>
                    <button class="quick-action-btn" onclick="handleQuickAction('track')">Track Order</button>
                    <button class="quick-action-btn" onclick="handleQuickAction('support')">Get Support</button>
                </div>

                <div class="chat-input">
                    <input type="text" id="message-input" placeholder="Type your message..." />
                    <button id="send-btn" class="send-btn">
            <ion-icon name="send-outline"></ion-icon>
          </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Add chatbot script -->
    <script src="./assets/js/chatbot.js"></script>

</body>

</html>