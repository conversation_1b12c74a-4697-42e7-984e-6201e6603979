const Orders = () => {
  const [orders, setOrders] = React.useState([]);
  const [isLoading, setIsLoading] = React.useState(true);
  const [error, setError] = React.useState(null);
  const [filter, setFilter] = React.useState('all');
  const [dateFilter, setDateFilter] = React.useState('all');

  React.useEffect(() => {
    loadOrders();
  }, [filter, dateFilter]);

  const loadOrders = async () => {
    try {
      const user = JSON.parse(localStorage.getItem('user'));
      if (!user) {
        throw new Error('User not found');
      }

      // Get orders from localStorage and sessionStorage
      const localStorageOrders = JSON.parse(localStorage.getItem('orders')) || [];
      const sessionOrders = Object.keys(sessionStorage)
        .filter(key => key.startsWith('order_'))
        .map(key => JSON.parse(sessionStorage.getItem(key)))
        .filter(order => order && order.email === user.email);

      // Combine orders
      let allOrders = [...sessionOrders, ...localStorageOrders]
        .filter(order => order && order.email === user.email);

      // Apply status filter
      if (filter !== 'all') {
        allOrders = allOrders.filter(order => 
          order.status.toLowerCase() === filter.toLowerCase()
        );
      }

      // Apply date filter
      if (dateFilter !== 'all') {
        const now = new Date();
        const cutoffDate = new Date();
        
        switch (dateFilter) {
          case 'today':
            cutoffDate.setHours(0, 0, 0, 0);
            break;
          case 'week':
            cutoffDate.setDate(now.getDate() - 7);
            break;
          case 'month':
            cutoffDate.setMonth(now.getMonth() - 1);
            break;
          case '3months':
            cutoffDate.setMonth(now.getMonth() - 3);
            break;
          default:
            break;
        }

        allOrders = allOrders.filter(order => 
          new Date(order.date) >= cutoffDate
        );
      }

      // Sort by date (newest first)
      allOrders.sort((a, b) => new Date(b.date) - new Date(a.date));

      setOrders(allOrders);
    } catch (err) {
      setError('Failed to load orders');
      console.error('Error loading orders:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusClass = (status) => {
    switch (status.toLowerCase()) {
      case 'pending':
        return 'status-pending';
      case 'processing':
        return 'status-processing';
      case 'shipped':
        return 'status-shipped';
      case 'delivered':
        return 'status-delivered';
      case 'cancelled':
        return 'status-cancelled';
      default:
        return 'status-pending';
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 2
    }).format(amount);
  };

  if (isLoading) {
    return <div className="loading">Loading orders...</div>;
  }

  if (error) {
    return <div className="error-message">{error}</div>;
  }

  return (
    <div className="orders-section">
      <div className="orders-header">
        <h2>Your Orders</h2>
        <div className="filters">
          <div className="filter-group">
            <label htmlFor="statusFilter">Status:</label>
            <select 
              id="statusFilter" 
              value={filter}
              onChange={(e) => setFilter(e.target.value)}
              className="filter-select"
            >
              <option value="all">All Orders</option>
              <option value="pending">Pending</option>
              <option value="processing">Processing</option>
              <option value="shipped">Shipped</option>
              <option value="delivered">Delivered</option>
              <option value="cancelled">Cancelled</option>
            </select>
          </div>
          <div className="filter-group">
            <label htmlFor="dateFilter">Time Period:</label>
            <select 
              id="dateFilter" 
              value={dateFilter}
              onChange={(e) => setDateFilter(e.target.value)}
              className="filter-select"
            >
              <option value="all">All Time</option>
              <option value="today">Today</option>
              <option value="week">Last 7 Days</option>
              <option value="month">Last 30 Days</option>
              <option value="3months">Last 3 Months</option>
            </select>
          </div>
        </div>
      </div>

      {orders.length === 0 ? (
        <div className="no-orders">
          <h3>No Orders Found</h3>
          <p>No orders match your current filters or you haven't placed any orders yet.</p>
          <a href="/" className="shop-now-btn">Start Shopping</a>
        </div>
      ) : (
        <div className="orders-list">
          {orders.map(order => (
            <div key={order.orderId} className="order-card">
              <div className="order-header">
                <div className="order-info">
                  <h3>Order #{order.orderId}</h3>
                  <p>Placed on {formatDate(order.date)}</p>
                </div>
                <div className="order-status">
                  <span className={`status-badge ${getStatusClass(order.status)}`}>
                    {order.status}
                  </span>
                </div>
              </div>
              
              <div className="order-items">
                {order.cart.map(item => (
                  <div key={item.id} className="order-item">
                    <img src={item.image} alt={item.name} className="item-image" />
                    <div className="item-details">
                      <h4>{item.name}</h4>
                      <p>Quantity: {item.quantity}</p>
                      <p>Price: {formatCurrency(item.price)}</p>
                      <p>Total: {formatCurrency(item.price * item.quantity)}</p>
                    </div>
                  </div>
                ))}
              </div>
              
              <div className="order-summary">
                <div className="summary-row">
                  <span>Subtotal:</span>
                  <span>{formatCurrency(order.subtotal || order.total)}</span>
                </div>
                {order.shipping && (
                  <div className="summary-row">
                    <span>Shipping:</span>
                    <span>{formatCurrency(order.shipping)}</span>
                  </div>
                )}
                {order.tax && (
                  <div className="summary-row">
                    <span>Tax:</span>
                    <span>{formatCurrency(order.tax)}</span>
                  </div>
                )}
                <div className="summary-row total">
                  <span>Total:</span>
                  <span>{formatCurrency(order.total)}</span>
                </div>
              </div>

              <div className="order-footer">
                <div className="shipping-info">
                  <h4>Shipping Address</h4>
                  <p>{order.shippingAddress?.addressLine}</p>
                  <p>{order.shippingAddress?.city}, {order.shippingAddress?.state} {order.shippingAddress?.zip}</p>
                </div>
                <div className="order-actions">
                  <a href={`order-confirmation.html?orderId=${order.orderId}`} className="action-btn view-btn">
                    <ion-icon name="eye-outline"></ion-icon>
                    View Details
                  </a>
                  <a href={`invoice-template.html?orderId=${order.orderId}`} className="action-btn invoice-btn">
                    <ion-icon name="document-outline"></ion-icon>
                    Download Invoice
                  </a>
                  {order.status === 'delivered' && (
                    <button className="action-btn review-btn">
                      <ion-icon name="star-outline"></ion-icon>
                      Write Review
                    </button>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}; 