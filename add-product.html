<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Product</title>
    <link rel="stylesheet" href="./assets/css/style.css">
    <style>
        .product-form {
            max-width: 600px;
            margin: 2rem auto;
            padding: 2rem;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #333;
        }
        
        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 1rem;
        }
        
        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #ff7f50;
            box-shadow: 0 0 0 3px rgba(255, 127, 80, 0.1);
        }
        
        .submit-btn {
            background: #ff7f50;
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1rem;
            transition: background 0.3s ease;
        }
        
        .submit-btn:hover {
            background: #ff6347;
        }
    </style>
</head>

<body>
    <div class="product-form">
        <h1>Add New Product</h1>
        <form id="addProductForm">
            <div class="form-group">
                <label for="productName">Product Name</label>
                <input type="text" id="productName" required>
            </div>
            <div class="form-group">
                <label for="productPrice">Price</label>
                <input type="number" id="productPrice" step="0.01" required>
            </div>
            <div class="form-group">
                <label for="productDescription">Description</label>
                <textarea id="productDescription" rows="4"></textarea>
            </div>
            <div class="form-group">
                <label for="productImage">Image URL</label>
                <input type="url" id="productImage" required>
            </div>
            <div class="form-group">
                <label for="productCategory">Category</label>
                <select id="productCategory" required>
          <option value="furniture">Furniture</option>
          <option value="decor">Decor</option>
          <option value="lighting">Lighting</option>
        </select>
            </div>
            <button type="submit" class="submit-btn">Add Product</button>
        </form>
    </div>

    <script>
        document.getElementById('addProductForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const product = {
                name: document.getElementById('productName').value,
                price: parseFloat(document.getElementById('productPrice').value),
                description: document.getElementById('productDescription').value,
                image: document.getElementById('productImage').value,
                category: document.getElementById('productCategory').value,
                id: Date.now().toString()
            };

            // Save product to localStorage
            let products = JSON.parse(localStorage.getItem('products') || '[]');
            products.push(product);
            localStorage.setItem('products', JSON.stringify(products));

            alert('Product added successfully!');
            window.location.href = 'index.html';
        });
    </script>
</body>

</html>