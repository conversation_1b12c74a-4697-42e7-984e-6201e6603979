// Database test utility
import mysql from 'mysql2/promise';

const dbConfig = {
    host: 'localhost',
    user: 'root',
    password: '',
    database: 'indian_furniture_house'
};

async function testDatabaseConnection() {
    let connection;
    try {
        // Test connection
        connection = await mysql.createConnection(dbConfig);
        console.log('✅ Database connection successful');

        // Test users table
        const [users] = await connection.execute('SELECT * FROM users LIMIT 1');
        console.log('✅ Users table accessible:', users.length > 0 ? 'Contains data' : 'Empty');

        // Test test user credentials
        const [testUser] = await connection.execute(
            'SELECT * FROM users WHERE email = ?', ['<EMAIL>']
        );
        console.log('✅ Test user found:', testUser.length > 0 ? 'Yes' : 'No');

        // Test user addresses
        const [addresses] = await connection.execute(
            'SELECT * FROM user_addresses WHERE user_id = ?', [testUser[0] ? .id || 'test123']
        );
        console.log('✅ User addresses:', addresses.length);

        // Test user favorites
        const [favorites] = await connection.execute(
            'SELECT * FROM user_favorites WHERE user_id = ?', [testUser[0] ? .id || 'test123']
        );
        console.log('✅ User favorites:', favorites.length);

        // Test user cart
        const [cart] = await connection.execute(
            'SELECT * FROM user_cart WHERE user_id = ?', [testUser[0] ? .id || 'test123']
        );
        console.log('✅ User cart items:', cart.length);

        return {
            success: true,
            testUser: testUser[0],
            addresses: addresses,
            favorites: favorites,
            cart: cart
        };
    } catch (error) {
        console.error('❌ Database test failed:', error);
        return {
            success: false,
            error: error.message
        };
    } finally {
        if (connection) {
            await connection.end();
        }
    }
}

// Function to test user operations
async function testUserOperations() {
    let connection;
    try {
        connection = await mysql.createConnection(dbConfig);

        // Test user creation
        const testUser = {
            id: 'test_' + Date.now(),
            email: `test${Date.now()}@example.com`,
            full_name: 'Test User',
            phone: '+91 9876543212'
        };

        await connection.execute(
            'INSERT INTO users (id, email, full_name, phone) VALUES (?, ?, ?, ?)', [testUser.id, testUser.email, testUser.full_name, testUser.phone]
        );
        console.log('✅ User creation successful');

        // Test address addition
        await connection.execute(
            'INSERT INTO user_addresses (user_id, address_line1, city, state, postal_code) VALUES (?, ?, ?, ?, ?)', [testUser.id, '123 Test St', 'Test City', 'Test State', '123456']
        );
        console.log('✅ Address addition successful');

        // Test favorites addition
        await connection.execute(
            'INSERT INTO user_favorites (user_id, product_id) VALUES (?, ?)', [testUser.id, 'test-product-1']
        );
        console.log('✅ Favorites addition successful');

        // Test cart addition
        await connection.execute(
            'INSERT INTO user_cart (user_id, product_id, quantity) VALUES (?, ?, ?)', [testUser.id, 'test-product-1', 1]
        );
        console.log('✅ Cart addition successful');

        // Clean up test data
        await connection.execute('DELETE FROM users WHERE id = ?', [testUser.id]);
        console.log('✅ Test data cleanup successful');

        return {
            success: true,
            message: 'All user operations tested successfully'
        };
    } catch (error) {
        console.error('❌ User operations test failed:', error);
        return {
            success: false,
            error: error.message
        };
    } finally {
        if (connection) {
            await connection.end();
        }
    }
}

// Run tests
async function runDatabaseTests() {
    console.log('🔄 Starting database tests...');

    const connectionTest = await testDatabaseConnection();
    console.log('\nConnection test results:', connectionTest);

    const operationsTest = await testUserOperations();
    console.log('\nOperations test results:', operationsTest);

    if (connectionTest.success && operationsTest.success) {
        console.log('\n✅ All database tests passed successfully!');
    } else {
        console.log('\n❌ Some tests failed. Please check the errors above.');
    }
}

// Run the tests
runDatabaseTests().catch(console.error);