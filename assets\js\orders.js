// Order Management System

class OrderManager {
    constructor() {
        this.orders = [];
        this.ordersContainer = document.getElementById('orders-container');
        this.filterSelect = document.getElementById('order-filter');
        this.refreshBtn = document.getElementById('refresh-orders');
        this.trackingModal = document.getElementById('tracking-modal');
        this.detailsModal = document.getElementById('order-details-modal');
        this.setupEventListeners();
    }

    setupEventListeners() {
        // Filter and refresh handlers
        if (this.filterSelect) {
            this.filterSelect.addEventListener('change', () => this.loadOrders());
        }
        if (this.refreshBtn) {
            this.refreshBtn.addEventListener('click', () => {
                localStorage.removeItem('orders');
                this.loadOrders();
            });
        }

        // Modal close handlers
        document.querySelectorAll('.close-modal').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const modal = e.target.closest('.modal');
                if (modal) this.hideModal(modal.id);
            });
        });

        // Close modal on outside click
        [this.trackingModal, this.detailsModal].forEach(modal => {
            if (modal) {
                modal.addEventListener('click', (e) => {
                    if (e.target === modal) {
                        this.hideModal(modal.id);
                    }
                });

                // Prevent modal content clicks from closing the modal
                const content = modal.querySelector('.modal-content');
                if (content) {
                    content.addEventListener('click', (e) => {
                        e.stopPropagation();
                    });
                }
            }
        });

        // Handle escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.hideAllModals();
            }
        });

        // Add navigation event listeners
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const targetId = link.getAttribute('href').substring(1);
                this.handleNavigation(targetId);
            });
        });
    }

    handleNavigation(targetId) {
        // Hide all sections
        document.querySelectorAll('.content-section').forEach(section => {
            section.style.display = 'none';
        });

        // Show target section
        const targetSection = document.getElementById(`${targetId}-section`);
        if (targetSection) {
            targetSection.style.display = 'block';
        }

        // Update active nav link
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href') === `#${targetId}`) {
                link.classList.add('active');
            }
        });

        // If navigating to orders, reload them
        if (targetId === 'orders') {
            this.loadOrders();
        }
    }

    showModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.add('show');
            document.body.style.overflow = 'hidden';
        }
    }

    hideModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.remove('show');
            document.body.style.overflow = '';
        }
    }

    hideAllModals() {
        document.querySelectorAll('.modal').forEach(modal => {
            modal.classList.remove('show');
        });
        document.body.style.overflow = '';
    }

    async loadOrders() {
        try {
            const user = JSON.parse(localStorage.getItem('user'));
            if (!user) {
                throw new Error('User not logged in');
            }

            this.showLoading();

            // Try to get orders from localStorage first
            let orders = JSON.parse(localStorage.getItem('orders'));

            if (!orders) {
                // If no orders in localStorage, fetch from API
                try {
                    const response = await fetch(`/api/user/orders?email=${encodeURIComponent(user.email)}`);
                    if (!response.ok) throw new Error('Failed to fetch orders');
                    orders = await response.json();

                    // Store orders in localStorage
                    localStorage.setItem('orders', JSON.stringify(orders));
                } catch (error) {
                    console.log('Using mock orders due to API error:', error);
                    // Use mock orders if API fails
                    orders = [{
                            orderId: 'ORD-001',
                            date: new Date().toISOString(),
                            total: 999.99,
                            status: 'pending',
                            items: [{
                                id: 'PROD-1',
                                name: 'Modern Sofa',
                                price: 999.99,
                                quantity: 1,
                                image: './assets/images/products/sofa.jpg'
                            }]
                        },
                        {
                            orderId: 'ORD-002',
                            date: new Date(Date.now() - 86400000).toISOString(),
                            total: 1499.99,
                            status: 'delivered',
                            items: [{
                                id: 'PROD-2',
                                name: 'Dining Table',
                                price: 1499.99,
                                quantity: 1,
                                image: './assets/images/products/table.jpg'
                            }]
                        }
                    ];
                }
            }

            this.orders = orders;

            // Apply filter if selected
            const filter = this.filterSelect ? this.filterSelect.value : 'all';
            const filteredOrders = filter !== 'all' ?
                orders.filter(order => order.status.toLowerCase() === filter.toLowerCase()) :
                orders;

            this.displayOrders(filteredOrders);
        } catch (error) {
            console.error('Error loading orders:', error);
            this.showError('Failed to load orders. Please try again later.');
        }
    }

    displayOrders(orders) {
        if (!this.ordersContainer) return;

        if (!orders || orders.length === 0) {
            this.showEmptyState();
            return;
        }

        this.ordersContainer.innerHTML = orders.map(order => this.createOrderCard(order)).join('');
    }

    createOrderCard(order) {
            return `
            <div class="order-card">
                <div class="order-header">
                    <div>
                        <h3>Order #${order.orderId}</h3>
                        <p class="order-date">
                            ${new Date(order.date).toLocaleDateString()}
                        </p>
                    </div>
                    <span class="status-badge status-${order.status.toLowerCase()}">
                        ${order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                    </span>
                </div>
                <div class="order-items">
                    ${order.items.map(item => `
                        <div class="order-item">
                            <img src="${item.image || './assets/images/placeholder.jpg'}" 
                                alt="${item.name}" 
                                class="order-item-image"
                                onerror="this.src='./assets/images/placeholder.jpg'">
                            <div class="item-details">
                                <h4>${item.name}</h4>
                                <p>Quantity: ${item.quantity}</p>
                                <p>₹${parseFloat(item.price).toFixed(2)}</p>
                            </div>
                        </div>
                    `).join('')}
                </div>
                <div class="order-footer">
                    <div class="order-total">
                        Total: ₹${parseFloat(order.total).toFixed(2)}
                    </div>
                    <div class="order-actions">
                        <button class="track-order-btn" onclick="orderManager.trackOrder('${order.orderId}')">
                            <i class="fas fa-truck"></i> Track Order
                        </button>
                        <button class="view-details-btn" onclick="orderManager.viewOrderDetails('${order.orderId}')">
                            <i class="fas fa-eye"></i> View Details
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    async trackOrder(orderId) {
        const order = this.orders.find(o => o.orderId === orderId);
        if (!order) return;

        try {
            // Try to fetch tracking data from API
            let trackingData;
            try {
                const response = await fetch(`/api/orders/${orderId}/tracking`);
                if (!response.ok) throw new Error('Failed to fetch tracking data');
                trackingData = await response.json();
            } catch (error) {
                console.log('Using mock tracking data:', error);
                // Use mock tracking data if API fails
                trackingData = {
                    pending: order.date,
                    processing: order.status === 'processing' ? new Date().toISOString() : null,
                    shipped: order.status === 'shipped' ? new Date().toISOString() : null,
                    delivered: order.status === 'delivered' ? new Date().toISOString() : null
                };
            }

            const modalContent = this.trackingModal.querySelector('.modal-body');
            modalContent.innerHTML = this.createTrackingTimeline(order, trackingData);
            this.showModal('tracking-modal');
        } catch (error) {
            console.error('Error showing tracking:', error);
            this.showError('Failed to load tracking information');
        }
    }

    createTrackingTimeline(order, trackingData) {
        const statuses = ['pending', 'processing', 'shipped', 'delivered'];
        const currentStatusIndex = statuses.indexOf(order.status.toLowerCase());

        return `
            <h2>Order Tracking - #${order.orderId}</h2>
            <div class="timeline">
                ${statuses.map((status, index) => `
                    <div class="timeline-item ${index <= currentStatusIndex ? 'completed' : ''} 
                                           ${index === currentStatusIndex ? 'current' : ''}">
                        <div class="timeline-marker"></div>
                        <div class="timeline-content">
                            <h4>${status.charAt(0).toUpperCase() + status.slice(1)}</h4>
                            <p>${this.getStatusDate(order, status, trackingData)}</p>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
    }

    viewOrderDetails(orderId) {
        const order = this.orders.find(o => o.orderId === orderId);
        if (!order) return;

        const modalContent = this.detailsModal.querySelector('.modal-body');
        modalContent.innerHTML = `
            <h2>Order Details - #${order.orderId}</h2>
            <div class="order-info">
                <p><strong>Order Date:</strong> ${new Date(order.date).toLocaleDateString()}</p>
                <p><strong>Status:</strong> 
                    <span class="status-badge status-${order.status.toLowerCase()}">
                        ${order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                    </span>
                </p>
            </div>
            <div class="order-items-detail">
                ${order.items.map(item => `
                    <div class="order-item">
                        <img src="${item.image || './assets/images/placeholder.jpg'}" 
                            alt="${item.name}" 
                            class="order-item-image"
                            onerror="this.src='./assets/images/placeholder.jpg'">
                        <div class="item-details">
                            <h4>${item.name}</h4>
                            <p>Quantity: ${item.quantity}</p>
                            <p>Price: ₹${parseFloat(item.price).toFixed(2)}</p>
                            <p>Subtotal: ₹${(item.price * item.quantity).toFixed(2)}</p>
                        </div>
                    </div>
                `).join('')}
            </div>
            <div class="order-summary">
                <h3>Order Summary</h3>
                <p><strong>Subtotal:</strong> ₹${order.total.toFixed(2)}</p>
                <p><strong>Shipping:</strong> Free</p>
                <p><strong>Total:</strong> ₹${order.total.toFixed(2)}</p>
            </div>
        `;

        this.showModal('order-details-modal');
    }

    getStatusDate(order, status, trackingData) {
        if (trackingData && trackingData[status]) {
            return new Date(trackingData[status]).toLocaleDateString();
        }
        
        // Fallback dates if tracking data is not available
        const baseDate = new Date(order.date);
        const dates = {
            pending: baseDate,
            processing: new Date(baseDate.getTime() + 24*60*60*1000),
            shipped: new Date(baseDate.getTime() + 2*24*60*60*1000),
            delivered: new Date(baseDate.getTime() + 4*24*60*60*1000)
        };
        return dates[status].toLocaleDateString();
    }

    showLoading() {
        if (!this.ordersContainer) return;
        this.ordersContainer.innerHTML = `
            <div class="loading-spinner"></div>
        `;
    }

    showError(message) {
        if (!this.ordersContainer) return;
        this.ordersContainer.innerHTML = `
            <div class="error-message">
                <i class="fas fa-exclamation-circle"></i>
                ${message}
            </div>
        `;
    }

    showEmptyState() {
        if (!this.ordersContainer) return;
        this.ordersContainer.innerHTML = `
            <div class="no-orders">
                <i class="fas fa-shopping-bag"></i>
                <h3>No Orders Found</h3>
                <p>Start shopping to see your orders here!</p>
                <a href="index.html" class="shop-button">Start Shopping</a>
            </div>
        `;
    }
}

// Initialize order manager and make it globally available
window.orderManager = new OrderManager();

// Load orders when the page loads
document.addEventListener('DOMContentLoaded', () => {
    if (window.orderManager) {
        window.orderManager.loadOrders();
    }
});