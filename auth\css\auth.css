/* Auth Pages Styles */

:root {
    --primary-color: #4a90e2;
    --error-color: #e74c3c;
    --success-color: #2ecc71;
    --text-color: #2c3e50;
    --border-color: #dcdde1;
    --background-color: #f5f6fa;
}

body {
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    background-color: var(--background-color);
    color: var(--text-color);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.auth-container {
    width: 100%;
    max-width: 400px;
    padding: 20px;
}

.auth-box {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

h2 {
    margin: 0 0 1.5rem;
    text-align: center;
    color: var(--text-color);
    font-weight: 600;
}

.form-group {
    margin-bottom: 1.5rem;
}

label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-color);
}

input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
    box-sizing: border-box;
}

input:focus {
    outline: none;
    border-color: var(--primary-color);
}

.btn-primary {
    width: 100%;
    padding: 0.75rem;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 1rem;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.btn-primary:hover {
    background-color: #357abd;
}

.auth-link {
    text-align: center;
    margin-top: 1.5rem;
    font-size: 0.9rem;
}

.auth-link a {
    color: var(--primary-color);
    text-decoration: none;
}

.auth-link a:hover {
    text-decoration: underline;
}

.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 1rem 1.5rem;
    border-radius: 4px;
    color: white;
    font-size: 0.9rem;
    display: none;
    animation: slideIn 0.3s ease;
    z-index: 1000;
}

.notification.error {
    background-color: var(--error-color);
}

.notification.success {
    background-color: var(--success-color);
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@media (max-width: 480px) {
    .auth-container {
        padding: 10px;
    }
    .auth-box {
        padding: 1.5rem;
    }
    .notification {
        left: 20px;
        right: 20px;
    }
}