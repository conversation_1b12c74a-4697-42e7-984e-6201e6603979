/**
 * Advanced Chatbot functionality for Indian Furniture House
 * Features:
 * - Context awareness
 * - Typing indicators
 * - Message history
 * - Smart suggestions
 * - Rich media responses
 * - Voice input support
 * - Responsive design
 */
document.addEventListener("DOMContentLoaded", function() {
    console.log("Advanced Chatbot initialized");

    class ChatBot {
        constructor() {
            // Chatbot state management
            this.chatbotState = {
                context: null,
                conversationHistory: [],
                isTyping: false,
                lastUserMessage: null,
                suggestedActions: [],
                voiceInputActive: false,
                currentCategory: null,
                currentPriceRange: null
            };

            // Product categories and their descriptions
            this.categories = {
                'Sofas': {
                    description: 'Comfortable and stylish sofas for your living room',
                    subcategories: ['3-Seater', '2-Seater', 'Sectional', 'Recliner']
                },
                'Beds': {
                    description: 'Quality beds for a good night\'s sleep',
                    subcategories: ['King Size', 'Queen Size', 'Single', 'Bunk Beds']
                },
                'Dining Tables': {
                    description: 'Elegant dining tables for your family',
                    subcategories: ['6-Seater', '4-Seater', 'Round', 'Extendable']
                },
                'Chairs': {
                    description: 'Stylish chairs for every room',
                    subcategories: ['Dining', 'Office', 'Lounge', 'Outdoor']
                },
                'Storage': {
                    description: 'Storage solutions for your home',
                    subcategories: ['Wardrobes', 'Cabinets', 'Bookshelves', 'TV Units']
                },
                'Decor': {
                    description: 'Beautiful decorative items for your home',
                    subcategories: ['Wall Art', 'Mirrors', 'Vases', 'Lighting']
                }
            };

            // Price ranges
            this.priceRanges = {
                'budget': { min: 0, max: 10000, label: 'Under ₹10,000' },
                'mid-range': { min: 10000, max: 30000, label: '₹10,000 - ₹30,000' },
                'premium': { min: 30000, max: Infinity, label: 'Above ₹30,000' }
            };

            // Common queries and responses
            this.commonQueries = {
                'payment': {
                    keywords: ['payment', 'pay', 'upi', 'card', 'cash'],
                    response: "We accept various payment methods:\n" +
                        "• Credit/Debit Cards\n" +
                        "• UPI (Google Pay, PhonePe, Paytm)\n" +
                        "• Net Banking\n" +
                        "• Cash on Delivery (for orders under ₹30,000)\n\n" +
                        "All online payments are secure and encrypted.",
                    suggestions: ["Payment Methods", "Place Order", "Contact Support"]
                },
                'delivery': {
                    keywords: ['delivery', 'shipping', 'transport'],
                    response: "Our delivery service includes:\n" +
                        "• Free delivery within city limits\n" +
                        "• Standard delivery: 3-5 business days\n" +
                        "• Express delivery available\n" +
                        "• Professional installation service\n" +
                        "• Real-time tracking\n\n" +
                        "Shipping costs for other areas are calculated based on distance and item size.",
                    suggestions: ["Track Order", "Delivery Rates", "Installation Service"]
                },
                'returns': {
                    keywords: ['return', 'refund', 'cancel'],
                    response: "Our return policy:\n" +
                        "• 30-day return window\n" +
                        "• Items must be in original condition\n" +
                        "• Free returns for defective items\n" +
                        "• Custom furniture non-returnable unless defective\n" +
                        "• Refunds processed within 5-7 business days\n\n" +
                        "Need to return something? We're here to help!",
                    suggestions: ["Return Process", "Refund Status", "Contact Support"]
                },
                'warranty': {
                    keywords: ['warranty', 'guarantee', 'repair'],
                    response: "Warranty Information:\n" +
                        "• 1-year warranty on manufacturing defects\n" +
                        "• 3-year warranty on premium furniture\n" +
                        "• Extended warranty options available\n" +
                        "• Free repairs during warranty period\n" +
                        "• Professional maintenance services\n\n" +
                        "Register your product for warranty coverage!",
                    suggestions: ["Warranty Registration", "Service Request", "Maintenance Tips"]
                },
                'material': {
                    keywords: ['material', 'wood', 'fabric', 'leather'],
                    response: "We use premium materials:\n" +
                        "• Solid Wood: Teak, Oak, Sheesham\n" +
                        "• Fabrics: Premium cotton, linen, velvet\n" +
                        "• Leather: Genuine and faux options\n" +
                        "• Metal: Stainless steel, wrought iron\n" +
                        "All materials meet quality standards.",
                    suggestions: ["Material Guide", "Care Instructions", "Custom Options"]
                }
            };

            // Get chatbot elements
            this.chatbotWidget = document.getElementById("chatbot-widget");
            this.chatbotToggle = document.getElementById("chatbot-toggle");
            this.chatbotClose = document.getElementById("chatbot-close");
            this.chatbotMinimize = document.getElementById("chatbot-minimize");
            this.chatMessages = document.getElementById("chat-messages");
            this.messageInput = document.getElementById("message-input");
            this.sendButton = document.getElementById("send-btn");
            this.chatbotBadge = document.querySelector(".chatbot-badge");

            // Initialize Web Speech API for voice input
            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            this.recognition = null;
            if (SpeechRecognition) {
                this.recognition = new SpeechRecognition();
                this.recognition.continuous = false;
                this.recognition.lang = 'en-US';
            }

            // Debug element existence
            this.validateElements();

            // Element validation
            this.validateElements();

            // Event Handlers
            this.initializeEventHandlers();
        }

        validateElements() {
            const elements = {
                chatbotWidget: this.chatbotWidget,
                chatbotToggle: this.chatbotToggle,
                chatbotClose: this.chatbotClose,
                chatbotMinimize: this.chatbotMinimize,
                chatMessages: this.chatMessages,
                messageInput: this.messageInput,
                sendButton: this.sendButton,
                chatbotBadge: this.chatbotBadge
            };

            console.log("Chatbot elements validation:",
                Object.entries(elements)
                .map(([key, value]) => `${key}: ${!!value}`)
                .join(', ')
            );

            if (!this.chatbotWidget || !this.chatbotToggle || !this.chatMessages) {
                console.error("Critical chatbot elements missing");
                return false;
            }
            return true;
        }

        initializeEventHandlers() {
            // Toggle chatbot
            this.chatbotToggle.onclick = this.handleChatbotToggle.bind(this);

            // Close and minimize handlers
            if (this.chatbotClose) this.chatbotClose.onclick = () => this.closeChatbot();
            if (this.chatbotMinimize) this.chatbotMinimize.onclick = () => this.minimizeChatbot();

            // Message input handlers
            if (this.messageInput && this.sendButton) {
                this.sendButton.onclick = () => this.handleMessageSend();
                this.messageInput.onkeypress = (e) => {
                    if (e.key === 'Enter') this.handleMessageSend();
                };
                this.messageInput.oninput = () => this.handleTyping();
            }

            // Voice input handler
            if (this.recognition) {
                const voiceButton = this.createVoiceInputButton();
                this.messageInput.parentElement.appendChild(voiceButton);
            }

            // Handle window resize for responsiveness
            window.onresize = this.debounce(() => this.adjustChatbotSize(), 250);
        }

        handleChatbotToggle() {
            this.chatbotWidget.classList.toggle("active");
            if (this.chatbotWidget.classList.contains("active")) {
                this.messageInput ? .focus();
                this.chatbotBadge.style.display = "none";
                this.loadChatHistory();
                this.adjustChatbotSize();
            }
        }

        closeChatbot() {
            this.chatbotWidget.classList.remove("active");
            this.saveChatHistory();
        }

        minimizeChatbot() {
            this.chatbotWidget.classList.remove("active");
            this.showNotification("Chat minimized. Click to restore.");
        }

        // Message Handling
        async handleMessageSend() {
            const message = this.messageInput.value.trim();
            if (!message) return;

            this.messageInput.value = '';
            await this.sendMessage(message, true);
            this.handleBotResponse(message);
        }

        // Enhanced message sending with rich media support
        async sendMessage(message, isUser = true) {
            if (!this.chatMessages) return;

            const messageDiv = document.createElement("div");
            messageDiv.className = `chat-message ${isUser ? "user" : "bot"}`;

            const now = new Date();
            const timeString = now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

            // Create message content with rich media support
            messageDiv.innerHTML = this.createMessageHTML(message, isUser, timeString);

            // Animate message appearance
            messageDiv.style.opacity = "0";
            this.chatMessages.appendChild(messageDiv);
            await this.animateElement(messageDiv, "fadeIn");

            // Scroll to latest message
            this.smoothScrollToBottom(this.chatMessages);

            // Save to history
            this.saveChatHistory();
        }

        // Bot Response Handling
        async handleBotResponse(userMessage) {
            this.showTypingIndicator();

            // Update context
            this.updateContext(userMessage);

            // Get bot response with context
            const response = await this.getBotResponse(userMessage);

            // Process response
            await this.processResponse(response);

            this.hideTypingIndicator();
            this.updateSuggestedActions(response);
        }

        // Context Management
        updateContext(message) {
            this.chatbotState.lastUserMessage = message;
            this.chatbotState.context = this.analyzeContext(message);
        }

        analyzeContext(message) {
            const context = {
                intent: this.detectIntent(message),
                entities: this.extractEntities(message),
                sentiment: this.analyzeSentiment(message),
                previousContext: this.chatbotState.context
            };
            return context;
        }

        // Enhanced Bot Response
        async getBotResponse(message) {
            const context = this.chatbotState.context;
            const response = await this.generateContextualResponse(message, context);
            return response;
        }

        // Enhanced Bot Response Generation
        async generateContextualResponse(message, context) {
            const normalizedMessage = message.toLowerCase();

            // Check for common queries first
            for (const [queryType, queryData] of Object.entries(this.commonQueries)) {
                if (queryData.keywords.some(keyword => normalizedMessage.includes(keyword))) {
                    return {
                        text: queryData.response,
                        suggestions: queryData.suggestions
                    };
                }
            }

            // Product category queries
            for (const [category, data] of Object.entries(this.categories)) {
                if (normalizedMessage.toLowerCase().includes(category.toLowerCase())) {
                    this.chatbotState.currentCategory = category;
                    return {
                        text: `${data.description}. Available types:\n` +
                            data.subcategories.map(sub => `• ${sub}`).join('\n') +
                            '\n\nWould you like to see our collection?',
                        richMedia: true,
                        products: await this.getProductsByCategory(category),
                        suggestions: ["View Collection", "Price Range", "Compare Items"]
                    };
                }
            }

            // Price queries
            if (normalizedMessage.includes('price') || normalizedMessage.includes('cost')) {
                for (const [range, data] of Object.entries(this.priceRanges)) {
                    if (normalizedMessage.includes(range)) {
                        this.chatbotState.currentPriceRange = range;
                        return {
                            text: `Here are our ${range} options ${data.label}:`,
                            richMedia: true,
                            products: await this.getProductsByPriceRange(data.min, data.max),
                            suggestions: ["View Details", "Other Price Ranges", "Special Offers"]
                        };
                    }
                }
            }

            // Order tracking
            if (normalizedMessage.includes('track') || normalizedMessage.includes('order status')) {
                return {
                    text: "To track your order, please:\n" +
                        "1. Enter your order ID, or\n" +
                        "2. Log in to your account\n" +
                        "3. Check your email for tracking updates\n\n" +
                        "Need help finding your order?",
                    suggestions: ["Enter Order ID", "Login", "Contact Support"]
                };
            }

            // Availability check
            if (normalizedMessage.includes('available') || normalizedMessage.includes('stock')) {
                return {
                    text: "I can help you check item availability. Please:\n" +
                        "1. Provide the product name or ID\n" +
                        "2. Select from categories\n" +
                        "3. Browse our in-stock items\n\n" +
                        "What would you like to check?",
                    suggestions: ["Browse Categories", "Search by ID", "In-Stock Items"]
                };
            }

            // Customization options
            if (normalizedMessage.includes('custom') || normalizedMessage.includes('personalize')) {
                return {
                    text: "We offer customization options:\n" +
                        "• Material selection\n" +
                        "• Color choices\n" +
                        "• Size modifications\n" +
                        "• Design alterations\n\n" +
                        "What would you like to customize?",
                    suggestions: ["Custom Furniture", "Material Options", "Get Quote"]
                };
            }

            // Default greeting
            if (this.isGreeting(normalizedMessage)) {
                return {
                    text: "👋 Welcome to Indian Furniture House! I can help you with:\n" +
                        "1. Finding perfect furniture\n" +
                        "2. Price information\n" +
                        "3. Delivery details\n" +
                        "4. Order tracking\n" +
                        "5. Custom requirements\n\n" +
                        "What are you looking for today?",
                    suggestions: ["Browse Products", "Today's Deals", "Custom Orders"]
                };
            }

            // Default response
            return {
                text: "I'm here to help! You can ask me about:\n" +
                    "• Our furniture collection\n" +
                    "• Prices and deals\n" +
                    "• Delivery options\n" +
                    "• Custom orders\n" +
                    "• Order tracking\n\n" +
                    "How can I assist you?",
                suggestions: ["Show Categories", "Special Offers", "Contact Support"]
            };
        }

        async getProductsByCategory(category) {
            try {
                // Fetch products from your database
                // This is a mock implementation
                return [{
                    name: `${category} Sample`,
                    price: "₹20,000",
                    image: `./assets/images/products/${category.toLowerCase()}.jpg`,
                    description: `High-quality ${category.toLowerCase()} with premium finish`,
                    link: `#${category.toLowerCase()}`
                }];
            } catch (error) {
                console.error('Error fetching products:', error);
                return [];
            }
        }

        async getProductsByPriceRange(min, max) {
            try {
                // Fetch products within price range
                // This is a mock implementation
                return [{
                    name: "Sample Product",
                    price: `₹${min + 1000}`,
                    image: "./assets/images/products/sample.jpg",
                    description: "Quality product within your budget",
                    link: "#sample"
                }];
            } catch (error) {
                console.error('Error fetching products:', error);
                return [];
            }
        }

        createProductCard(product) {
            return `
                <div class="product-card">
                    <img src="${product.image}" alt="${product.name}" class="product-image">
                    <h3>${product.name}</h3>
                    <p class="price">${product.price}</p>
                    <p class="description">${product.description}</p>
                    <button onclick="window.location.href='${product.link}'" class="view-details">View Details</button>
                </div>
            `;
        }

        // Rich Media Message Creation
        createMessageHTML(message, isUser, timeString) {
            const avatar = isUser ?
                '<ion-icon name="person-outline"></ion-icon>' :
                '<ion-icon name="person-circle-outline"></ion-icon>';

            let content = message;
            if (!isUser) {
                content = this.formatBotMessage(message);
                content = this.addRichMediaElements(content);
            }

            return `
                <div class="message-avatar">${avatar}</div>
                <div class="message-content">
                    <div class="message-bubble">${content}</div>
                    <span class="message-time">${timeString}</span>
                </div>
            `;
        }

        // Rich Media Support
        addRichMediaElements(content) {
            // Add support for images
            content = content.replace(/\[image:(.*?)\]/g, (match, url) =>
                `<img src="${url}" alt="Product Image" class="message-image">`
            );

            // Add support for buttons
            content = content.replace(/\[button:(.*?):(.*?)\]/g, (match, text, action) =>
                `<button class="message-button" onclick="handleButtonAction('${action}')">${text}</button>`
            );

            // Add support for product cards
            content = content.replace(/\[product:(.*?):(.*?):(.*?):(.*?)\]/g,
                (match, title, price, image, link) => this.createProductCard(title, price, image, link)
            );

            return content;
        }

        // Utility Functions
        debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        async animateElement(element, animation) {
            return new Promise(resolve => {
                element.style.animation = `${animation} 0.3s ease forwards`;
                element.addEventListener('animationend', () => resolve(), { once: true });
            });
        }

        smoothScrollToBottom(element) {
            element.scrollTo({
                top: element.scrollHeight,
                behavior: 'smooth'
            });
        }

        // Chat History Management
        saveChatHistory() {
            const history = Array.from(this.chatMessages.children).map(msg => ({
                content: msg.querySelector('.message-content').innerHTML,
                isUser: msg.classList.contains('user'),
                timestamp: new Date().toISOString()
            }));
            localStorage.setItem('chatHistory', JSON.stringify(history));
        }

        loadChatHistory() {
            const history = JSON.parse(localStorage.getItem('chatHistory') || '[]');
            this.chatMessages.innerHTML = '';
            history.forEach(msg => {
                this.sendMessage(msg.content, msg.isUser);
            });
        }

        // Responsive Design
        adjustChatbotSize() {
            const isMobile = window.innerWidth <= 768;
            if (isMobile) {
                this.chatbotWidget.style.width = '100%';
                this.chatbotWidget.style.height = '100%';
                this.chatbotWidget.style.maxHeight = '100vh';
            } else {
                this.chatbotWidget.style.width = '350px';
                this.chatbotWidget.style.height = '500px';
                this.chatbotWidget.style.maxHeight = '80vh';
            }
        }

        // Voice Input Support
        createVoiceInputButton() {
            const button = document.createElement('button');
            button.className = 'voice-input-btn';
            button.innerHTML = '<ion-icon name="mic-outline"></ion-icon>';
            button.onclick = this.toggleVoiceInput.bind(this);
            return button;
        }

        toggleVoiceInput() {
            if (this.chatbotState.voiceInputActive) {
                this.stopVoiceInput();
            } else {
                this.startVoiceInput();
            }
        }

        startVoiceInput() {
            if (!this.recognition) return;

            this.chatbotState.voiceInputActive = true;
            this.recognition.start();
            this.showNotification("Listening...");

            this.recognition.onresult = (event) => {
                const transcript = event.results[0][0].transcript;
                this.messageInput.value = transcript;
                this.handleMessageSend();
            };

            this.recognition.onerror = (event) => {
                console.error("Voice recognition error:", event.error);
                this.stopVoiceInput();
            };
        }

        stopVoiceInput() {
            if (!this.recognition) return;

            this.chatbotState.voiceInputActive = false;
            this.recognition.stop();
            this.showNotification("Voice input stopped");
        }

        // Initialize chatbot
        initialize() {
            this.adjustChatbotSize();
            setTimeout(() => {
                this.sendMessage("👋 Welcome to Indian Furniture House! I'm your AI shopping assistant. I can help you with:\n" +
                    "1. Finding perfect furniture for your home\n" +
                    "2. Checking prices and availability\n" +
                    "3. Tracking orders and delivery\n" +
                    "4. Returns and refunds\n" +
                    "5. Special offers and discounts\n\n" +
                    "How can I assist you today?", false);
            }, 1000);
        }
    }

    // Start the chatbot
    const chatbot = new ChatBot();
    chatbot.initialize();
});

// Initialize chatbot
function initialize() {
    adjustChatbotSize();
    setTimeout(() => {
        sendMessage("👋 Welcome to Indian Furniture House! I'm your AI shopping assistant. I can help you with:\n" +
            "1. Finding perfect furniture for your home\n" +
            "2. Checking prices and availability\n" +
            "3. Tracking orders and delivery\n" +
            "4. Returns and refunds\n" +
            "5. Special offers and discounts\n\n" +
            "How can I assist you today?", false);
    }, 1000);
}

// Start the chatbot
initialize();