class AuthService {
    constructor() {
        // Initialize Firebase Auth
        this.auth = firebase.auth();
    }

    async register(userData) {
        try {
            // Create user with email and password
            const userCredential = await this.auth.createUserWithEmailAndPassword(
                userData.email,
                userData.password
            );

            // Update user profile
            await userCredential.user.updateProfile({
                displayName: userData.full_name
            });

            // Store additional user data
            const user = {
                uid: userCredential.user.uid,
                email: userData.email,
                full_name: userData.full_name,
                phone: userData.phone,
                created_at: new Date().toISOString()
            };

            // Store user data in localStorage
            this.setUser(user);
            return user;
        } catch (error) {
            console.error('Registration error:', error);
            throw new Error(this.getErrorMessage(error));
        }
    }

    async login(credentials) {
        try {
            const userCredential = await this.auth.signInWithEmailAndPassword(
                credentials.email,
                credentials.password
            );

            const user = {
                uid: userCredential.user.uid,
                email: userCredential.user.email,
                full_name: userCredential.user.displayName,
                phone: userCredential.user.phoneNumber
            };

            this.setUser(user);
            return user;
        } catch (error) {
            console.error('Login error:', error);
            throw new Error(this.getErrorMessage(error));
        }
    }

    async loginWithGoogle() {
        try {
            const provider = new firebase.auth.GoogleAuthProvider();
            const result = await this.auth.signInWithPopup(provider);

            const user = {
                uid: result.user.uid,
                email: result.user.email,
                full_name: result.user.displayName,
                phone: result.user.phoneNumber,
                photo_url: result.user.photoURL
            };

            this.setUser(user);
            return user;
        } catch (error) {
            console.error('Google login error:', error);
            throw new Error(this.getErrorMessage(error));
        }
    }

    async loginWithFacebook() {
        try {
            const provider = new firebase.auth.FacebookAuthProvider();
            const result = await this.auth.signInWithPopup(provider);

            const user = {
                uid: result.user.uid,
                email: result.user.email,
                full_name: result.user.displayName,
                phone: result.user.phoneNumber,
                photo_url: result.user.photoURL
            };

            this.setUser(user);
            return user;
        } catch (error) {
            console.error('Facebook login error:', error);
            throw new Error(this.getErrorMessage(error));
        }
    }

    logout() {
        this.auth.signOut();
        localStorage.removeItem('user');
        window.location.href = '/auth/login.html';
    }

    getUser() {
        const user = localStorage.getItem('user');
        return user ? JSON.parse(user) : null;
    }

    setUser(user) {
        localStorage.setItem('user', JSON.stringify(user));
    }

    isAuthenticated() {
        return !!this.auth.currentUser;
    }

    getErrorMessage(error) {
        switch (error.code) {
            case 'auth/email-already-in-use':
                return 'This email is already registered. Please use a different email or try logging in.';
            case 'auth/invalid-email':
                return 'Please enter a valid email address.';
            case 'auth/operation-not-allowed':
                return 'This operation is not allowed. Please contact support.';
            case 'auth/weak-password':
                return 'Please choose a stronger password. It should be at least 8 characters long.';
            case 'auth/user-disabled':
                return 'This account has been disabled. Please contact support.';
            case 'auth/user-not-found':
                return 'No account found with this email. Please register first.';
            case 'auth/wrong-password':
                return 'Incorrect password. Please try again.';
            default:
                return error.message || 'An error occurred. Please try again.';
        }
    }

    async updateProfile(userData) {
        try {
            const response = await fetch(`${this.baseUrl}/auth/profile`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.getToken()}`
                },
                body: JSON.stringify(userData)
            });

            if (!response.ok) {
                const error = await response.json();
                throw new Error(error.message || 'Profile update failed');
            }

            const data = await response.json();
            this.setUser(data.user);
            return data;
        } catch (error) {
            throw error;
        }
    }

    async changePassword(passwordData) {
        try {
            const response = await fetch(`${this.baseUrl}/auth/change-password`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.getToken()}`
                },
                body: JSON.stringify(passwordData)
            });

            if (!response.ok) {
                const error = await response.json();
                throw new Error(error.message || 'Password change failed');
            }

            return await response.json();
        } catch (error) {
            throw error;
        }
    }
}

// Create a singleton instance
const authService = new AuthService();