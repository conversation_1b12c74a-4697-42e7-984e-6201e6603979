<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Account - Indian Furniture House</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/account.css">
    <style>
        body {
            background-color: #f5f5f5;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .register-container {
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
        }
        
        .register-container h1 {
            color: #333;
            margin-bottom: 1.5rem;
            font-size: 1.8rem;
            text-align: center;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: #555;
            font-weight: 500;
        }
        
        .form-group input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 1rem;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #ff7f50;
            box-shadow: 0 0 0 2px rgba(255, 127, 80, 0.1);
        }
        
        .form-group .hint {
            font-size: 0.8rem;
            color: #666;
            margin-top: 0.25rem;
        }
        
        .create-account-btn {
            width: 100%;
            padding: 0.75rem;
            background: #ff7f50;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            margin-top: 1rem;
            transition: background-color 0.2s;
        }
        
        .create-account-btn:hover {
            background: #ff6347;
        }
        
        .login-link {
            text-align: center;
            margin-top: 1.5rem;
            color: #666;
        }
        
        .login-link a {
            color: #ff7f50;
            text-decoration: none;
            font-weight: 500;
        }
        
        .login-link a:hover {
            text-decoration: underline;
        }
        /* Error styles */
        
        .error-message {
            background: #fee2e2;
            color: #dc2626;
            padding: 0.75rem;
            border-radius: 4px;
            margin-bottom: 1rem;
            font-size: 0.9rem;
            display: none;
        }
        
        .error-message.show {
            display: block;
        }
        /* Loading state */
        
        .create-account-btn.loading {
            background: #ffaa90;
            cursor: not-allowed;
        }
    </style>
    @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap'); * { margin: 0; padding: 0; box-sizing: border-box; font-family: 'Poppins', sans-serif; } :root { --primary-color: #ff7f50; --primary-dark: #ff6347;
    --background: #1a1a1a; --text-light: #ffffff; --text-dark: #333333; } body { min-height: 100vh; display: flex; align-items: center; justify-content: center; background: var(--background); padding: 2rem; position: relative; overflow: hidden; } /* 3D
    Background Elements */ .background-shapes { position: fixed; top: 0; left: 0; width: 100%; height: 100%; z-index: 0; perspective: 1000px; } .shape { position: absolute; background: rgba(255, 127, 80, 0.1); backdrop-filter: blur(5px); border: 1px solid
    rgba(255, 255, 255, 0.1); border-radius: 50%; animation: float 10s infinite ease-in-out; } .shape:nth-child(1) { width: 200px; height: 200px; top: 20%; left: 10%; animation-delay: 0s; transform: rotate(45deg); } .shape:nth-child(2) { width: 300px;
    height: 300px; top: 60%; right: 15%; animation-delay: -2s; transform: rotate(30deg); } .shape:nth-child(3) { width: 150px; height: 150px; bottom: 20%; left: 20%; animation-delay: -4s; transform: rotate(60deg); } @keyframes float { 0%, 100% { transform:
    translateY(0) rotate(0deg) translateZ(0); } 50% { transform: translateY(-20px) rotate(10deg) translateZ(50px); } } /* Grid Background */ .grid { position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-image: linear-gradient(rgba(255,
    127, 80, 0.05) 1px, transparent 1px), linear-gradient(90deg, rgba(255, 127, 80, 0.05) 1px, transparent 1px); background-size: 50px 50px; perspective: 1000px; transform-style: preserve-3d; animation: gridMove 20s linear infinite; } @keyframes gridMove
    { 0% { transform: translateZ(0) rotate(0deg); } 100% { transform: translateZ(100px) rotate(360deg); } } /* Main Container */ .container { position: relative; z-index: 1; width: 100%; max-width: 1200px; display: flex; gap: 2rem; perspective: 2000px;
    } .left-section { flex: 1; padding: 3rem; background: rgba(255, 255, 255, 0.03); backdrop-filter: blur(10px); border: 1px solid rgba(255, 255, 255, 0.1); border-radius: 20px; transform: rotateY(-5deg); transform-style: preserve-3d; transition: transform
    0.5s ease; } .left-section:hover { transform: rotateY(0deg); } .right-section { flex: 1; padding: 3rem; background: rgba(255, 255, 255, 0.05); backdrop-filter: blur(10px); border: 1px solid rgba(255, 255, 255, 0.1); border-radius: 20px; transform:
    rotateY(5deg); transform-style: preserve-3d; transition: transform 0.5s ease; } .right-section:hover { transform: rotateY(0deg); } /* Brand Logo */ .brand-logo { text-align: center; margin-bottom: 2rem; transform: translateZ(30px); } .brand-logo i
    { font-size: 3rem; color: var(--primary-color); margin-bottom: 1rem; display: inline-block; animation: pulse 2s infinite; } @keyframes pulse { 0%, 100% { transform: scale(1); } 50% { transform: scale(1.1); } } .brand-logo h2 { color: var(--text-light);
    font-size: 1.8rem; font-weight: 600; text-transform: uppercase; letter-spacing: 2px; } /* Content */ .content h1 { color: var(--text-light); font-size: 2.5rem; margin-bottom: 1rem; transform: translateZ(20px); } .content p { color: rgba(255, 255,
    255, 0.7); margin-bottom: 2rem; transform: translateZ(15px); } .features { list-style: none; margin-top: 2rem; } .feature-item { color: var(--text-light); margin-bottom: 1.5rem; display: flex; align-items: center; gap: 1rem; transform: translateZ(10px);
    opacity: 0; animation: slideIn 0.5s forwards; } .feature-item i { color: var(--primary-color); font-size: 1.2rem; } @keyframes slideIn { to { opacity: 1; transform: translateX(0) translateZ(10px); } } /* Form */ .form-group { margin-bottom: 1.5rem;
    transform: translateZ(20px); } .form-group label { display: block; color: var(--text-light); margin-bottom: 0.5rem; font-size: 0.9rem; font-weight: 500; } .form-group input { width: 100%; padding: 1rem; background: rgba(255, 255, 255, 0.05); border:
    1px solid rgba(255, 255, 255, 0.1); border-radius: 8px; color: var(--text-light); font-size: 1rem; transition: all 0.3s ease; } .form-group input:focus { outline: none; border-color: var(--primary-color); box-shadow: 0 0 20px rgba(255, 127, 80, 0.2);
    } .form-group small { display: block; color: rgba(255, 255, 255, 0.6); margin-top: 0.5rem; font-size: 0.8rem; } .btn-primary { width: 100%; padding: 1rem; background: var(--primary-color); color: var(--text-light); border: none; border-radius: 8px;
    font-size: 1rem; font-weight: 600; cursor: pointer; transition: all 0.3s ease; transform: translateZ(30px); } .btn-primary:hover { background: var(--primary-dark); transform: translateY(-2px) translateZ(30px); box-shadow: 0 10px 20px rgba(255, 127,
    80, 0.3); } .auth-links { text-align: center; margin-top: 2rem; color: var(--text-light); transform: translateZ(10px); } .auth-links a { color: var(--primary-color); text-decoration: none; font-weight: 500; transition: color 0.3s ease; } .auth-links
    a:hover { color: var(--primary-dark); } /* Notification */ .notification { position: fixed; bottom: 20px; right: 20px; padding: 1rem 2rem; border-radius: 8px; color: white; transform: translateY(100px); opacity: 0; transition: all 0.3s ease; z-index:
    1000; } .notification.show { transform: translateY(0); opacity: 1; } .notification.error { background: rgba(220, 38, 38, 0.9); border-left: 4px solid #dc2626; } .notification.success { background: rgba(22, 163, 74, 0.9); border-left: 4px solid #16a34a;
    } /* Responsive Design */ @media (max-width: 1024px) { .container { max-width: 900px; } } @media (max-width: 768px) { .container { flex-direction: column; padding: 1rem; } .left-section, .right-section { transform: none; padding: 2rem; } .features
    { display: none; } }
    </style>
</head>

<body>
    <!-- Background Elements -->
    <div class="background-shapes">
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
    </div>
    <div class="grid"></div>

    <!-- Main Content -->
    <div class="container">
        <div class="left-section">
            <div class="brand-logo">
                <i class="fas fa-couch"></i>
                <h2>Indian Furniture House</h2>
            </div>
            <div class="content">
                <h1>Welcome</h1>
                <p>Join our community of furniture enthusiasts and discover amazing pieces for your home.</p>
                <ul class="features">
                    <li class="feature-item" style="animation-delay: 0.2s">
                        <i class="fas fa-check-circle"></i>
                        <span>Access exclusive deals and offers</span>
                    </li>
                    <li class="feature-item" style="animation-delay: 0.4s">
                        <i class="fas fa-check-circle"></i>
                        <span>Track your orders in real-time</span>
                    </li>
                    <li class="feature-item" style="animation-delay: 0.6s">
                        <i class="fas fa-check-circle"></i>
                        <span>Save your favorite items</span>
                    </li>
                    <li class="feature-item" style="animation-delay: 0.8s">
                        <i class="fas fa-check-circle"></i>
                        <span>Get personalized recommendations</span>
                    </li>
                </ul>
            </div>
        </div>

        <div class="right-section">
            <form id="registerForm">
                <div class="form-group">
                    <label for="fullName">Full Name</label>
                    <input type="text" id="fullName" name="fullName" required minlength="2" maxlength="100">
                </div>
                <div class="form-group">
                    <label for="email">Email</label>
                    <input type="email" id="email" name="email" required>
                </div>
                <div class="form-group">
                    <label for="phone">Phone Number</label>
                    <input type="tel" id="phone" name="phone" required pattern="[0-9]{10,15}">
                </div>
                <div class="form-group">
                    <label for="password">Password</label>
                    <input type="password" id="password" name="password" required minlength="8">
                    <small>Password must be at least 8 characters long</small>
                </div>
                <div class="form-group">
                    <label for="confirmPassword">Confirm Password</label>
                    <input type="password" id="confirmPassword" name="confirmPassword" required>
                </div>
                <button type="submit" class="btn-primary" id="submitBtn">Create Account</button>
                <div class="auth-links">
                    <a href="login.html">Already have an account? Sign in</a>
                </div>
            </form>
        </div>
    </div>

    <div id="notification" class="notification"></div>

    <!-- Firebase Scripts -->
    <script src="https://www.gstatic.com/firebasejs/9.6.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.6.1/firebase-auth-compat.js"></script>

    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyAGVoqQlSR15zRCrlJqYsvz7b2FVOO8Kog",
            authDomain: "project-1-ee121.firebaseapp.com",
            projectId: "project-1-ee121",
            storageBucket: "project-1-ee121.firebasestorage.app",
            messagingSenderId: "************",
            appId: "1:************:web:7a5d1c89e7406b0f1e5476"
        };

        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);

        class AuthService {
            constructor() {
                this.auth = firebase.auth();
            }

            async register(userData) {
                try {
                    const userCredential = await this.auth.createUserWithEmailAndPassword(
                        userData.email,
                        userData.password
                    );

                    await userCredential.user.updateProfile({
                        displayName: userData.full_name
                    });

                    const user = {
                        uid: userCredential.user.uid,
                        email: userData.email,
                        full_name: userData.full_name,
                        phone: userData.phone,
                        created_at: new Date().toISOString()
                    };

                    localStorage.setItem('user', JSON.stringify(user));
                    return user;
                } catch (error) {
                    console.error('Registration error:', error);
                    throw new Error(this.getErrorMessage(error));
                }
            }

            getErrorMessage(error) {
                switch (error.code) {
                    case 'auth/email-already-in-use':
                        return 'This email is already registered. Please use a different email or try logging in.';
                    case 'auth/invalid-email':
                        return 'Please enter a valid email address.';
                    case 'auth/operation-not-allowed':
                        return 'This operation is not allowed. Please contact support.';
                    case 'auth/weak-password':
                        return 'Please choose a stronger password. It should be at least 8 characters long.';
                    default:
                        return error.message || 'An error occurred. Please try again.';
                }
            }
        }

        const authService = new AuthService();

        function showNotification(message, type = 'error') {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.className = `notification ${type}`;
            notification.classList.add('show');

            setTimeout(() => {
                notification.classList.remove('show');
            }, 5000);
        }

        function setLoading(isLoading) {
            const submitBtn = document.getElementById('submitBtn');
            if (isLoading) {
                submitBtn.disabled = true;
                submitBtn.textContent = 'Creating Account...';
            } else {
                submitBtn.disabled = false;
                submitBtn.textContent = 'Create Account';
            }
        }

        function validateForm(formData) {
            const {
                fullName,
                email,
                phone,
                password,
                confirmPassword
            } = formData;

            if (fullName.length < 2) {
                throw new Error('Full name must be at least 2 characters long');
            }

            if (!email.match(/[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,}$/i)) {
                throw new Error('Please enter a valid email address');
            }

            if (!phone.match(/^[0-9]{10,15}$/)) {
                throw new Error('Please enter a valid phone number (10-15 digits)');
            }

            if (password.length < 8) {
                throw new Error('Password must be at least 8 characters long');
            }

            if (password !== confirmPassword) {
                throw new Error('Passwords do not match');
            }
        }

        document.getElementById('registerForm').addEventListener('submit', async(e) => {
            e.preventDefault();
            setLoading(true);

            try {
                const formData = {
                    fullName: document.getElementById('fullName').value.trim(),
                    email: document.getElementById('email').value.trim(),
                    phone: document.getElementById('phone').value.trim(),
                    password: document.getElementById('password').value,
                    confirmPassword: document.getElementById('confirmPassword').value
                };

                validateForm(formData);

                await authService.register({
                    full_name: formData.fullName,
                    email: formData.email,
                    phone: formData.phone,
                    password: formData.password
                });

                showNotification('Registration successful! Redirecting...', 'success');
                setTimeout(() => {
                    window.location.href = '../account.html';
                }, 2000);
            } catch (error) {
                showNotification(error.message);
                setLoading(false);
            }
        });
    </script>
</body>

</html>