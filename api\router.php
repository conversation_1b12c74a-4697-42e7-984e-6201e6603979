<?php
class Router {
    private $routes = [];
    private $db;

    public function __construct($db) {
        $this->db = $db;
    }

    public function addRoute($method, $path, $handler) {
        $this->routes[] = [
            'method' => $method,
            'path' => $path,
            'handler' => $handler
        ];
    }

    public function handleRequest($method, $path) {
        foreach ($this->routes as $route) {
            $pattern = $this->pathToPattern($route['path']);
            if ($route['method'] === $method && preg_match($pattern, $path, $matches)) {
                array_shift($matches); // Remove full match
                return call_user_func_array($route['handler'], array_merge([$this->db], $matches));
            }
        }
        
        http_response_code(404);
        return ['error' => 'Route not found'];
    }

    private function pathToPattern($path) {
        return '#^' . preg_replace('/\{([a-zA-Z]+)\}/', '([^/]+)', $path) . '$#';
    }
}

// Example usage:
// $router = new Router($db);
// $router->addRoute('GET', '/users/{id}', function($db, $id) { ... });
// $result = $router->handleRequest($_SERVER['REQUEST_METHOD'], $_SERVER['PATH_INFO']); 