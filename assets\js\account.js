import { auth, db } from '../auth/js/auth-config.js';
import { doc, getDoc, updateDoc, collection, addDoc, deleteDoc, query, where, getDocs } from 'firebase/firestore';
import { updateProfile } from 'firebase/auth';
import { onAuthStateChanged, signOut } from "https://www.gstatic.com/firebasejs/9.6.1/firebase-auth.js";
import mockApi from './mock-api.js';

// Constants
const API_BASE_URL = 'http://localhost:3000/api';
const USE_MOCK_API = true; // Toggle this to switch between mock and real API

// Initialize Firebase
const firebaseConfig = {
    apiKey: "AIzaSyAGVoqQlSR15zRCrlJqYsvz7b2FVOO8Kog",
    authDomain: "project-1-ee121.firebaseapp.com",
    projectId: "project-1-ee121",
    storageBucket: "project-1-ee121.firebasestorage.app",
    messagingSenderId: "737534293699",
    appId: "1:737534293699:web:7a5d1c89e7406b0f1e5476"
};

firebase.initializeApp(firebaseConfig);

// DOM Elements
const profileSection = document.getElementById('profileSection');
const addressesSection = document.getElementById('addressesSection');
const ordersSection = document.getElementById('ordersSection');
const favoritesSection = document.getElementById('favoritesSection');

const editProfileBtn = document.getElementById('editProfileBtn');
const addAddressBtn = document.getElementById('addAddressBtn');

const editProfileModal = document.getElementById('editProfileModal');
const addAddressModal = document.getElementById('addAddressModal');

const profileForm = document.getElementById('profileForm');
const addressForm = document.getElementById('addressForm');

// Loading States
let isLoading = false;

// Current User Data
let currentUser = null;
let userProfile = null;

// Initialize Account Page
async function initAccountPage() {
    try {
        // Check if user is authenticated
        currentUser = auth.currentUser;
        if (!currentUser) {
            window.location.href = '/login.html';
            return;
        }

        // Load user profile
        await loadUserProfile();

        // Load addresses
        await loadAddresses();

        // Load orders
        await loadOrders();

        // Load favorites
        await loadFavorites();

        // Setup event listeners
        setupEventListeners();

    } catch (error) {
        console.error('Error initializing account page:', error);
        showNotification('Error loading account data', 'error');
    }
}

// Load User Profile
async function loadUserProfile() {
    try {
        const userDoc = await getDoc(doc(db, 'users', currentUser.uid));
        if (userDoc.exists()) {
            userProfile = userDoc.data();
            displayUserProfile();
        }
    } catch (error) {
        console.error('Error loading user profile:', error);
        showNotification('Error loading profile', 'error');
    }
}

// Display User Profile
function displayUserProfile() {
    const profileInfo = document.querySelector('.profile-info');
    profileInfo.innerHTML = `
        <div class="info-group">
            <label>Full Name</label>
            <p>${userProfile.fullName || 'Not set'}</p>
        </div>
        <div class="info-group">
            <label>Email</label>
            <p>${currentUser.email}</p>
        </div>
        <div class="info-group">
            <label>Phone</label>
            <p>${userProfile.phone || 'Not set'}</p>
        </div>
    `;
}

// Load Addresses
async function loadAddresses() {
    try {
        const addressesQuery = query(
            collection(db, 'addresses'),
            where('userId', '==', currentUser.uid)
        );
        const addressesSnapshot = await getDocs(addressesQuery);
        const addresses = [];

        addressesSnapshot.forEach(doc => {
            addresses.push({ id: doc.id, ...doc.data() });
        });

        displayAddresses(addresses);
    } catch (error) {
        console.error('Error loading addresses:', error);
        showNotification('Error loading addresses', 'error');
    }
}

// Display Addresses
function displayAddresses(addresses) {
    const addressesGrid = document.querySelector('.addresses-grid');
    addressesGrid.innerHTML = addresses.map(address => `
        <div class="address-card">
            ${address.isDefault ? '<span class="default-badge">Default</span>' : ''}
            <p>${address.addressLine}</p>
            <p>${address.city}, ${address.state} ${address.pinCode}</p>
            <button class="delete-btn" data-address-id="${address.id}">
                <i class="fas fa-trash"></i>
            </button>
        </div>
    `).join('');
}

// Load Orders
async function loadOrders() {
    try {
        const response = await fetch('/api/orders/user', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            },
            credentials: 'include'
        });

        if (!response.ok) {
            throw new Error('Failed to fetch orders');
        }

        const orders = await response.json();
        displayOrders(orders);
    } catch (error) {
        console.error('Error loading orders:', error);
        showNotification('Error loading orders', 'error');
    }
}

// Display Orders
function displayOrders(orders) {
    const ordersSection = document.getElementById('ordersSection');
    if (!ordersSection) return;

    // Create the header section
    const headerHtml = `
        <div class="section-header">
            <h2>My Orders</h2>
            <button class="btn-all-orders" onclick="viewAllOrders()">
                All Orders <i class="fas fa-chevron-right"></i>
            </button>
        </div>
    `;

    // Create the orders grid
    const ordersGridHtml = `
        <div class="orders-grid">
            ${orders.length === 0 ? getNoOrdersHtml() : getOrdersHtml(orders)}
        </div>
    `;

    ordersSection.innerHTML = headerHtml + ordersGridHtml;
}

// Get HTML for no orders state
function getNoOrdersHtml() {
    return `
        <div class="no-orders">
            <i class="fas fa-shopping-bag"></i>
            <h3>No Orders Yet</h3>
            <p>When you place an order, it will appear here</p>
            <a href="/products.html" class="btn btn-primary">Start Shopping</a>
        </div>
    `;
}

// Get HTML for orders
function getOrdersHtml(orders) {
    return orders.map(order => `
        <div class="order-card">
            <div class="order-header">
                <div class="order-id">
                    <h3>Order #${order.id}</h3>
                    <span class="order-date">${formatDate(order.orderDate)}</span>
                </div>
                <div class="order-status">
                    <span class="status-badge status-${order.status.toLowerCase()}">${order.status}</span>
                </div>
            </div>
            <div class="order-content">
                <div class="order-items">
                    ${getOrderItemsHtml(order.items)}
                </div>
                <div class="order-summary">
                    <div class="order-total">
                        <span>Total:</span>
                        <span class="amount">₹${order.total.toFixed(2)}</span>
                    </div>
                    <div class="order-actions">
                        ${getOrderActionsHtml(order)}
                    </div>
                </div>
            </div>
        </div>
    `).join('');
}

// Get HTML for order items
function getOrderItemsHtml(items) {
    return items.map(item => `
        <div class="order-item">
            <div class="item-image">
                <img src="${item.image || item.image_url}" alt="${item.name}">
            </div>
            <div class="item-details">
                <h4>${item.name}</h4>
                <div class="item-meta">
                    <span class="quantity">Qty: ${item.quantity}</span>
                    <span class="price">₹${(item.price_at_time || item.price).toFixed(2)}</span>
                </div>
            </div>
        </div>
    `).join('');
}

// Get HTML for order actions
function getOrderActionsHtml(order) {
    const actions = [];

    // View Details button
    actions.push(`
        <button class="btn btn-outline" onclick="viewOrderDetails('${order.id}')">
            <i class="fas fa-eye"></i> View Details
        </button>
    `);

    // Cancel button for pending orders
    if (order.status.toLowerCase() === 'pending') {
        actions.push(`
            <button class="btn btn-danger" onclick="cancelOrder('${order.id}')">
                <i class="fas fa-times"></i> Cancel
            </button>
        `);
    }

    // Track button for shipped orders
    if (order.status.toLowerCase() === 'shipped') {
        actions.push(`
            <button class="btn btn-track" onclick="trackOrder('${order.id}')">
                <i class="fas fa-truck"></i> Track
            </button>
        `);
    }

    return actions.join('');
}

// Format date
function formatDate(dateString) {
    const options = {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    };
    return new Date(dateString).toLocaleDateString('en-US', options);
}

// View all orders
function viewAllOrders() {
    window.location.href = '/orders.html';
}

// Track order
async function trackOrder(orderId) {
    try {
        const response = await fetch(`/api/orders/${orderId}/tracking`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            },
            credentials: 'include'
        });

        if (!response.ok) {
            throw new Error('Failed to fetch tracking information');
        }

        const tracking = await response.json();
        showTrackingModal(tracking);
    } catch (error) {
        console.error('Error tracking order:', error);
        showNotification('Error fetching tracking information', 'error');
    }
}

// Show tracking modal
function showTrackingModal(tracking) {
    const modal = document.createElement('div');
    modal.className = 'modal tracking-modal';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h2>Order Tracking</h2>
                <button class="close-btn">&times;</button>
            </div>
            <div class="modal-body">
                <div class="tracking-timeline">
                    ${tracking.events.map(event => `
                        <div class="tracking-event ${event.completed ? 'completed' : ''}">
                            <div class="event-icon">
                                <i class="fas ${getTrackingIcon(event.type)}"></i>
                            </div>
                            <div class="event-details">
                                <h4>${event.status}</h4>
                                <p>${event.description}</p>
                                <span class="event-date">${formatDate(event.timestamp)}</span>
                            </div>
                        </div>
                    `).join('')}
                </div>
                ${tracking.estimatedDelivery ? `
                    <div class="estimated-delivery">
                        <i class="fas fa-clock"></i>
                        <span>Estimated Delivery: ${formatDate(tracking.estimatedDelivery)}</span>
                    </div>
                ` : ''}
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    modal.style.display = 'block';

    // Close modal functionality
    const closeBtn = modal.querySelector('.close-btn');
    closeBtn.onclick = () => modal.remove();
    window.onclick = (event) => {
        if (event.target === modal) modal.remove();
    };
}

// Get tracking icon based on event type
function getTrackingIcon(type) {
    const icons = {
        'order_placed': 'fa-shopping-cart',
        'payment_confirmed': 'fa-credit-card',
        'processing': 'fa-cog',
        'shipped': 'fa-truck',
        'out_for_delivery': 'fa-shipping-fast',
        'delivered': 'fa-check-circle'
    };
    return icons[type] || 'fa-circle';
}

// Load Favorites
async function loadFavorites() {
    try {
        const favoritesQuery = query(
            collection(db, 'favorites'),
            where('userId', '==', currentUser.uid)
        );
        const favoritesSnapshot = await getDocs(favoritesQuery);
        const favorites = [];
        
        favoritesSnapshot.forEach(doc => {
            favorites.push({ id: doc.id, ...doc.data() });
        });

        displayFavorites(favorites);
    } catch (error) {
        console.error('Error loading favorites:', error);
        showNotification('Error loading favorites', 'error');
    }
}

// Display Favorites
function displayFavorites(favorites) {
    const favoritesGrid = document.querySelector('.favorites-grid');
    favoritesGrid.innerHTML = favorites.map(favorite => `
        <div class="favorite-card">
            <img src="${favorite.image}" alt="${favorite.name}">
            <div class="favorite-info">
                <h4>${favorite.name}</h4>
                <p>$${favorite.price.toFixed(2)}</p>
            </div>
            <button class="remove-btn" data-favorite-id="${favorite.id}">
                <i class="fas fa-times"></i>
                </button>
        </div>
    `).join('');
}

// Setup Event Listeners
function setupEventListeners() {
    // Edit Profile
    editProfileBtn.addEventListener('click', () => {
        editProfileModal.style.display = 'block';
        profileForm.fullName.value = userProfile.fullName || '';
        profileForm.phone.value = userProfile.phone || '';
    });

    // Add Address
    addAddressBtn.addEventListener('click', () => {
        addAddressModal.style.display = 'block';
        addressForm.reset();
    });

    // Close Modals
    document.querySelectorAll('.modal').forEach(modal => {
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.style.display = 'none';
            }
        });
    });

    // Profile Form Submit
    profileForm.addEventListener('submit', handleProfileUpdate);

    // Address Form Submit
    addressForm.addEventListener('submit', handleAddAddress);

    // Delete Address
    document.addEventListener('click', async (e) => {
        if (e.target.closest('.delete-btn')) {
            const addressId = e.target.closest('.delete-btn').dataset.addressId;
            await deleteAddress(addressId);
        }
    });

    // Remove Favorite
    document.addEventListener('click', async (e) => {
        if (e.target.closest('.remove-btn')) {
            const favoriteId = e.target.closest('.remove-btn').dataset.favoriteId;
            await removeFavorite(favoriteId);
        }
    });
}

// Handle Profile Update
async function handleProfileUpdate(e) {
    e.preventDefault();
    if (isLoading) return;

    try {
        isLoading = true;
        const fullName = profileForm.fullName.value.trim();
        const phone = profileForm.phone.value.trim();

        // Update Firestore
        await updateDoc(doc(db, 'users', currentUser.uid), {
            fullName,
            phone,
            updatedAt: new Date()
        });

        // Update Firebase Auth Profile
        await updateProfile(currentUser, {
            displayName: fullName
        });

        // Update local data and UI
        userProfile.fullName = fullName;
        userProfile.phone = phone;
        displayUserProfile();

        // Close modal and show success message
        editProfileModal.style.display = 'none';
        showNotification('Profile updated successfully', 'success');

    } catch (error) {
        console.error('Error updating profile:', error);
        showNotification('Error updating profile', 'error');
    } finally {
        isLoading = false;
    }
}

// Handle Add Address
async function handleAddAddress(e) {
    e.preventDefault();
    if (isLoading) return;

    try {
        isLoading = true;
        const addressData = {
            userId: currentUser.uid,
            addressLine: addressForm.addressLine.value.trim(),
            city: addressForm.city.value.trim(),
            state: addressForm.state.value.trim(),
            pinCode: addressForm.pinCode.value.trim(),
            isDefault: addressForm.isDefault.checked,
            createdAt: new Date()
        };

        // Add new address
        await addDoc(collection(db, 'addresses'), addressData);

        // Reload addresses
        await loadAddresses();

        // Close modal and show success message
        addAddressModal.style.display = 'none';
        addressForm.reset();
        showNotification('Address added successfully', 'success');

    } catch (error) {
        console.error('Error adding address:', error);
        showNotification('Error adding address', 'error');
    } finally {
        isLoading = false;
    }
}

// Delete Address
async function deleteAddress(addressId) {
    if (!confirm('Are you sure you want to delete this address?')) return;
    if (isLoading) return;

    try {
        isLoading = true;
        await deleteDoc(doc(db, 'addresses', addressId));
        await loadAddresses();
        showNotification('Address deleted successfully', 'success');
    } catch (error) {
        console.error('Error deleting address:', error);
        showNotification('Error deleting address', 'error');
    } finally {
        isLoading = false;
    }
}

// Remove Favorite
async function removeFavorite(favoriteId) {
    if (!confirm('Are you sure you want to remove this item from favorites?')) return;
    if (isLoading) return;

    try {
        isLoading = true;
        await deleteDoc(doc(db, 'favorites', favoriteId));
        await loadFavorites();
        showNotification('Item removed from favorites', 'success');
    } catch (error) {
        console.error('Error removing favorite:', error);
        showNotification('Error removing favorite', 'error');
    } finally {
        isLoading = false;
    }
}

// Show Notification
function showNotification(message, type = 'success') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    document.body.appendChild(notification);

    // Show notification
    setTimeout(() => notification.classList.add('show'), 100);

    // Remove notification
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => notification.remove(), 300);
    }, 3000);
}

// Initialize page when DOM is loaded
document.addEventListener('DOMContentLoaded', initAccountPage);