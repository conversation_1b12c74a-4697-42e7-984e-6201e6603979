<?php
require_once 'router.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle CORS preflight request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Database connection
try {
    $db = new PDO(
        'mysql:host=localhost;dbname=indian_furniture_house;charset=utf8mb4',
        'root',
        '',
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Database connection failed']);
    exit();
}

// Initialize router
$router = new Router($db);

// User routes
$router->addRoute('GET', '/users/{email}', function($db, $email) {
    $stmt = $db->prepare('SELECT id, email, name, phone FROM users WHERE email = ?');
    $stmt->execute([$email]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($user) {
        return $user;
    }
    
    http_response_code(404);
    return ['error' => 'User not found'];
});

$router->addRoute('PUT', '/users/{email}', function($db, $email) {
    $data = json_decode(file_get_contents('php://input'), true);
    
    $stmt = $db->prepare('UPDATE users SET name = ?, phone = ? WHERE email = ?');
    if ($stmt->execute([$data['name'], $data['phone'], $email])) {
        return ['message' => 'User updated successfully'];
    }
    
    http_response_code(500);
    return ['error' => 'Failed to update user'];
});

// Order routes
$router->addRoute('GET', '/orders', function($db) {
    $email = $_GET['email'] ?? null;
    if (!$email) {
        http_response_code(400);
        return ['error' => 'Email parameter is required'];
    }
    
    $stmt = $db->prepare('
        SELECT o.*, a.address, a.city, a.state, a.zip 
        FROM orders o 
        JOIN users u ON o.user_id = u.id 
        JOIN addresses a ON o.shipping_address_id = a.id 
        WHERE u.email = ? 
        ORDER BY o.created_at DESC
    ');
    $stmt->execute([$email]);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
});

// Address routes
$router->addRoute('GET', '/addresses', function($db) {
    $email = $_GET['email'] ?? null;
    if (!$email) {
        http_response_code(400);
        return ['error' => 'Email parameter is required'];
    }
    
    $stmt = $db->prepare('
        SELECT a.* 
        FROM addresses a 
        JOIN users u ON a.user_id = u.id 
        WHERE u.email = ?
    ');
    $stmt->execute([$email]);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
});

$router->addRoute('GET', '/addresses/{id}', function($db, $id) {
    $stmt = $db->prepare('SELECT * FROM addresses WHERE id = ?');
    $stmt->execute([$id]);
    $address = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($address) {
        return $address;
    }
    
    http_response_code(404);
    return ['error' => 'Address not found'];
});

$router->addRoute('POST', '/addresses', function($db) {
    $data = json_decode(file_get_contents('php://input'), true);
    
    // Get user ID
    $stmt = $db->prepare('SELECT id FROM users WHERE email = ?');
    $stmt->execute([$data['email']]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        http_response_code(404);
        return ['error' => 'User not found'];
    }
    
    $stmt = $db->prepare('
        INSERT INTO addresses (user_id, address, city, state, zip) 
        VALUES (?, ?, ?, ?, ?)
    ');
    
    if ($stmt->execute([
        $user['id'],
        $data['address'],
        $data['city'],
        $data['state'],
        $data['zip']
    ])) {
        return [
            'message' => 'Address added successfully',
            'id' => $db->lastInsertId()
        ];
    }
    
    http_response_code(500);
    return ['error' => 'Failed to add address'];
});

$router->addRoute('PUT', '/addresses/{id}', function($db, $id) {
    $data = json_decode(file_get_contents('php://input'), true);
    
    $stmt = $db->prepare('
        UPDATE addresses 
        SET address = ?, city = ?, state = ?, zip = ? 
        WHERE id = ?
    ');
    
    if ($stmt->execute([
        $data['address'],
        $data['city'],
        $data['state'],
        $data['zip'],
        $id
    ])) {
        return ['message' => 'Address updated successfully'];
    }
    
    http_response_code(500);
    return ['error' => 'Failed to update address'];
});

$router->addRoute('DELETE', '/addresses/{id}', function($db, $id) {
    $stmt = $db->prepare('DELETE FROM addresses WHERE id = ?');
    
    if ($stmt->execute([$id])) {
        return ['message' => 'Address deleted successfully'];
    }
    
    http_response_code(500);
    return ['error' => 'Failed to delete address'];
});

// Handle the request
$path = $_SERVER['PATH_INFO'] ?? '/';
$result = $router->handleRequest($_SERVER['REQUEST_METHOD'], $path);
echo json_encode($result); 