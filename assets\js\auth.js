import { auth } from "./firebase-config.js";
import {
    signInWithEmailAndPassword,
    createUserWithEmailAndPassword,
    signOut,
    GoogleAuthProvider,
    FacebookAuthProvider,
    signInWithPopup,
    onAuthStateChanged
} from "https://www.gstatic.com/firebasejs/9.6.1/firebase-auth.js";

const API_BASE_URL = 'http://localhost:3000/api';

// Function to handle user registration
export async function register(email, password, fullName, phone) {
    try {
        // First create user in Firebase
        const userCredential = await createUserWithEmailAndPassword(auth, email, password);
        const firebaseUser = userCredential.user;

        // Then create user in MySQL database
        const response = await fetch(`${API_BASE_URL}/users`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                id: firebaseUser.uid,
                email: email,
                full_name: fullName,
                phone: phone,
                firebase_uid: firebaseUser.uid
            })
        });

        if (!response.ok) {
            // If database creation fails, delete the Firebase user
            await firebaseUser.delete();
            throw new Error('Failed to create user in database');
        }

        const userData = await response.json();

        // Store user data in localStorage
        const userToStore = {
            ...userData,
            firebase_uid: firebaseUser.uid,
            email: firebaseUser.email
        };
        localStorage.setItem('user', JSON.stringify(userToStore));
        localStorage.setItem('isLoggedIn', 'true');

        return userToStore;
    } catch (error) {
        console.error('Registration error:', error);
        throw error;
    }
}

// Function to handle user login
export async function login(email, password) {
    try {
        // First authenticate with Firebase
        const userCredential = await signInWithEmailAndPassword(auth, email, password);
        const firebaseUser = userCredential.user;

        // Then get user data from our server
        const response = await fetch(`${API_BASE_URL}/users/email/${email}`);
        if (!response.ok) {
            throw new Error('User not found in database');
        }

        const userData = await response.json();

        // Store user data in localStorage
        const userToStore = {
            ...userData,
            firebase_uid: firebaseUser.uid,
            email: firebaseUser.email
        };
        localStorage.setItem('user', JSON.stringify(userToStore));
        localStorage.setItem('isLoggedIn', 'true');

        return userToStore;
    } catch (error) {
        console.error('Login error:', error);
        throw error;
    }
}

// Function to handle social login (Google/Facebook)
export async function socialLogin(provider) {
    try {
        const authProvider = provider === 'google' ? new GoogleAuthProvider() : new FacebookAuthProvider();
        const result = await signInWithPopup(auth, authProvider);
        const firebaseUser = result.user;

        // Check if user exists in MySQL database
        let response = await fetch(`${API_BASE_URL}/users/email/${firebaseUser.email}`);
        let userDetails;

        if (!response.ok) {
            // Create new user in MySQL if doesn't exist
            response = await fetch(`${API_BASE_URL}/users`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    id: firebaseUser.uid,
                    email: firebaseUser.email,
                    name: firebaseUser.displayName || 'User',
                    phone: firebaseUser.phoneNumber || '',
                    profile_image: firebaseUser.photoURL || null,
                    firebase_uid: firebaseUser.uid
                })
            });

            if (!response.ok) {
                throw new Error('Failed to create user in database');
            }
        }

        userDetails = await response.json();

        // Get additional user data
        const [addressesRes, ordersRes] = await Promise.all([
            fetch(`${API_BASE_URL}/users/${firebaseUser.uid}/addresses`),
            fetch(`${API_BASE_URL}/users/${firebaseUser.uid}/orders`)
        ]);

        const addresses = await addressesRes.json();
        const orders = await ordersRes.json();

        // Combine all user data
        const completeUserData = {
            ...userDetails,
            addresses: addresses || [],
            orders: orders || [],
            firebase_uid: firebaseUser.uid
        };

        // Store in localStorage
        localStorage.setItem('user', JSON.stringify(completeUserData));
        localStorage.setItem('isLoggedIn', 'true');

        return completeUserData;
    } catch (error) {
        console.error("Social login error:", error);
        throw error;
    }
}

// Function to handle user logout
export async function logout() {
    try {
        await signOut(auth);
        localStorage.removeItem('user');
        localStorage.setItem('isLoggedIn', 'false');
        window.location.href = 'login.html';
    } catch (error) {
        console.error('Logout error:', error);
        throw error;
    }
}

// Function to check authentication state
export function checkAuth() {
    return new Promise((resolve, reject) => {
        const unsubscribe = onAuthStateChanged(auth, async(user) => {
            unsubscribe(); // Unsubscribe immediately after first check

            if (user) {
                try {
                    // Verify user exists in our database
                    const response = await fetch(`${API_BASE_URL}/users/${user.uid}`);
                    if (!response.ok) {
                        throw new Error('User not found in database');
                    }

                    const userData = await response.json();
                    resolve({
                        ...userData,
                        firebase_uid: user.uid,
                        email: user.email
                    });
                } catch (error) {
                    reject(error);
                }
            } else {
                reject(new Error('Not authenticated'));
            }
        }, reject);
    });
}

// Function to update user profile
export async function updateProfile(userId, userData) {
    try {
        const response = await fetch(`${API_BASE_URL}/users/${userId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(userData)
        });

        if (!response.ok) {
            throw new Error('Failed to update profile');
        }

        const updatedUser = await response.json();

        // Update localStorage
        const currentUser = JSON.parse(localStorage.getItem('user'));
        const userToStore = {
            ...currentUser,
            ...updatedUser
        };
        localStorage.setItem('user', JSON.stringify(userToStore));

        return userToStore;
    } catch (error) {
        console.error('Profile update error:', error);
        throw error;
    }
}

// Function to get user data from MySQL
export async function getUserData(userId) {
    try {
        const [userRes, addressesRes, ordersRes] = await Promise.all([
            fetch(`${API_BASE_URL}/users/${userId}`),
            fetch(`${API_BASE_URL}/users/${userId}/addresses`),
            fetch(`${API_BASE_URL}/users/${userId}/orders`)
        ]);

        const [user, addresses, orders] = await Promise.all([
            userRes.json(),
            addressesRes.json(),
            ordersRes.json()
        ]);

        return {
            ...user,
            addresses: addresses || [],
            orders: orders || []
        };
    } catch (error) {
        console.error("Error fetching user data:", error);
        throw error;
    }
}

// Test login function with provided test credentials
export async function loginWithTestCredentials() {
    try {
        return await login('<EMAIL>', 'testpassword123');
    } catch (error) {
        console.error("Test login error:", error);
        throw error;
    }
}