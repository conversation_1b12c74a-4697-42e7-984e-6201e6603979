<?php
header('Content-Type: application/json');
require_once '../config/database.php';

session_start();

if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

$user_id = $_SESSION['user_id'];
$data = json_decode(file_get_contents('php://input'), true);

if (!isset($data['type']) || !isset($data['full_name']) || !isset($data['phone']) || 
    !isset($data['address']) || !isset($data['city']) || !isset($data['state']) || 
    !isset($data['zip'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Missing required fields']);
    exit;
}

try {
    // Start transaction
    $conn->begin_transaction();

    // Insert new address
    $stmt = $conn->prepare("
        INSERT INTO addresses (user_id, type, full_name, phone, address, city, state, zip, is_default, created_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, 0, NOW())
    ");
    $stmt->bind_param("isssssss", 
        $user_id,
        $data['type'],
        $data['full_name'],
        $data['phone'],
        $data['address'],
        $data['city'],
        $data['state'],
        $data['zip']
    );
    $stmt->execute();
    
    $conn->commit();
    echo json_encode(['success' => true, 'message' => 'Address saved successfully']);
} catch (Exception $e) {
    $conn->rollback();
    http_response_code(500);
    echo json_encode(['error' => 'Database error']);
}
?> 