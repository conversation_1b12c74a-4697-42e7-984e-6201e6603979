import mysql from 'mysql2/promise';
import { dbConfig } from './db-config.js';

// Create connection pool
const pool = mysql.createPool(dbConfig);

// Database utility functions
export async function getUserByEmail(email) {
    try {
        const [rows] = await pool.execute(
            'SELECT * FROM users WHERE email = ?', [email]
        );
        return rows[0];
    } catch (error) {
        console.error('Error fetching user:', error);
        throw error;
    }
}

export async function getUserById(userId) {
    try {
        const [rows] = await pool.execute(
            'SELECT * FROM users WHERE id = ?', [userId]
        );
        return rows[0];
    } catch (error) {
        console.error('Error fetching user:', error);
        throw error;
    }
}

export async function createUser(userData) {
    try {
        const { id, email, full_name, phone } = userData;
        const [result] = await pool.execute(
            'INSERT INTO users (id, email, full_name, phone) VALUES (?, ?, ?, ?)', [id, email, full_name, phone]
        );
        return { id, ...userData };
    } catch (error) {
        console.error('Error creating user:', error);
        throw error;
    }
}

export async function updateUser(userId, userData) {
    try {
        const { full_name, phone } = userData;
        const [result] = await pool.execute(
            'UPDATE users SET full_name = ?, phone = ? WHERE id = ?', [full_name, phone, userId]
        );
        return { id: userId, ...userData };
    } catch (error) {
        console.error('Error updating user:', error);
        throw error;
    }
}

export async function getUserAddresses(userId) {
    try {
        const [rows] = await pool.execute(
            'SELECT * FROM user_addresses WHERE user_id = ?', [userId]
        );
        return rows;
    } catch (error) {
        console.error('Error fetching addresses:', error);
        throw error;
    }
}

export async function addUserAddress(userId, addressData) {
    try {
        const { address_line1, city, state, postal_code, is_default } = addressData;
        const [result] = await pool.execute(
            'INSERT INTO user_addresses (user_id, address_line1, city, state, postal_code, is_default) VALUES (?, ?, ?, ?, ?, ?)', [userId, address_line1, city, state, postal_code, is_default]
        );
        return { id: result.insertId, ...addressData };
    } catch (error) {
        console.error('Error adding address:', error);
        throw error;
    }
}

export async function getUserFavorites(userId) {
    try {
        const [rows] = await pool.execute(
            'SELECT p.* FROM user_favorites uf JOIN products p ON uf.product_id = p.id WHERE uf.user_id = ?', [userId]
        );
        return rows;
    } catch (error) {
        console.error('Error fetching favorites:', error);
        throw error;
    }
}

export async function addToFavorites(userId, productId) {
    try {
        const [result] = await pool.execute(
            'INSERT INTO user_favorites (user_id, product_id) VALUES (?, ?)', [userId, productId]
        );
        return { userId, productId };
    } catch (error) {
        console.error('Error adding to favorites:', error);
        throw error;
    }
}

export async function getUserCart(userId) {
    try {
        const [rows] = await pool.execute(
            'SELECT p.*, uc.quantity FROM user_cart uc JOIN products p ON uc.product_id = p.id WHERE uc.user_id = ?', [userId]
        );
        return rows;
    } catch (error) {
        console.error('Error fetching cart:', error);
        throw error;
    }
}

export async function updateCart(userId, cartData) {
    try {
        // First, remove all existing cart items for this user
        await pool.execute('DELETE FROM user_cart WHERE user_id = ?', [userId]);

        // Then insert the new cart items
        for (const item of cartData) {
            await pool.execute(
                'INSERT INTO user_cart (user_id, product_id, quantity) VALUES (?, ?, ?)', [userId, item.id, item.quantity]
            );
        }
        return cartData;
    } catch (error) {
        console.error('Error updating cart:', error);
        throw error;
    }
}