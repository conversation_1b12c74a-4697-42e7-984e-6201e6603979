import { register } from './auth.js';

document.addEventListener('DOMContentLoaded', function() {
    const registerForm = document.getElementById('register-form');
    const errorMessage = document.getElementById('error-message');

    // Check if user is already logged in
    const isLoggedIn = localStorage.getItem('isLoggedIn') === 'true';
    if (isLoggedIn) {
        window.location.href = 'account.html';
        return;
    }

    registerForm.addEventListener('submit', async function(e) {
        e.preventDefault();

        const email = document.getElementById('email').value;
        const password = document.getElementById('password').value;
        const fullName = document.getElementById('name').value;
        const phone = document.getElementById('phone').value;

        try {
            // Show loading state
            const submitBtn = registerForm.querySelector('button[type="submit"]');
            const originalBtnText = submitBtn.textContent;
            submitBtn.disabled = true;
            submitBtn.textContent = 'Creating Account...';

            // Validate password
            if (password.length < 6) {
                throw new Error('Password must be at least 6 characters long');
            }

            // Validate phone number (optional)
            if (phone && !/^\+?[\d\s-]{10,}$/.test(phone)) {
                throw new Error('Please enter a valid phone number');
            }

            // Attempt registration
            await register(email, password, fullName, phone);

            // Redirect to account page
            window.location.href = 'account.html';
        } catch (error) {
            console.error('Registration error:', error);
            errorMessage.textContent = error.message || 'Failed to create account. Please try again.';
            errorMessage.style.display = 'block';

            // Reset button
            const submitBtn = registerForm.querySelector('button[type="submit"]');
            submitBtn.disabled = false;
            submitBtn.textContent = 'Create Account';
        }
    });

    // Google sign-up
    const googleSignupBtn = document.getElementById('google-signup');
    if (googleSignupBtn) {
        googleSignupBtn.addEventListener('click', async function(e) {
            e.preventDefault();
            try {
                const provider = new GoogleAuthProvider();
                const result = await signInWithPopup(auth, provider);
                const user = result.user;

                // Create user in SQL database
                const userData = {
                    id: user.uid,
                    email: user.email,
                    full_name: user.displayName || 'User',
                    phone: user.phoneNumber || ''
                };

                await createUser(userData);

                localStorage.setItem('user', JSON.stringify({
                    ...userData,
                    addresses: [],
                    cart: [],
                    favorites: []
                }));
                localStorage.setItem('isLoggedIn', 'true');

                window.location.href = 'account.html';
            } catch (error) {
                console.error('Google sign-up error:', error);
                alert(error.message);
            }
        });
    }

    // Facebook sign-up
    const facebookSignupBtn = document.getElementById('facebook-signup');
    if (facebookSignupBtn) {
        facebookSignupBtn.addEventListener('click', async function(e) {
            e.preventDefault();
            try {
                const provider = new FacebookAuthProvider();
                const result = await signInWithPopup(auth, provider);
                const user = result.user;

                // Create user in SQL database
                const userData = {
                    id: user.uid,
                    email: user.email,
                    full_name: user.displayName || 'User',
                    phone: user.phoneNumber || ''
                };

                await createUser(userData);

                localStorage.setItem('user', JSON.stringify({
                    ...userData,
                    addresses: [],
                    cart: [],
                    favorites: []
                }));
                localStorage.setItem('isLoggedIn', 'true');

                window.location.href = 'account.html';
            } catch (error) {
                console.error('Facebook sign-up error:', error);
                alert(error.message);
            }
        });
    }
});