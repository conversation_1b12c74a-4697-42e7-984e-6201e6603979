import { getAuth, signInWithEmailAndPassword, createUserWithEmailAndPassword, signOut, sendPasswordResetEmail } from "firebase/auth";
import config from './auth-config.js';

class AuthService {
    constructor() {
        this.baseUrl = 'http://localhost:3000/api';
        this.auth = getAuth(config.firebase);
        this.currentUser = null;
        this.auth.onAuthStateChanged(user => {
            this.currentUser = user;
            this.updateUIState(user);
        });
    }

    async login(email, password) {
        try {
            const userCredential = await signInWithEmailAndPassword(this.auth, email, password);
            const user = userCredential.user;

            const response = await fetch(`${this.baseUrl}/auth/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ email, password })
            });

            if (!response.ok) {
                const error = await response.json();
                throw new Error(error.message || 'Login failed');
            }

            const data = await response.json();
            localStorage.setItem('user', JSON.stringify({...data.user, firebaseUser: user }));
            localStorage.setItem('token', data.token);
            return data;
        } catch (error) {
            throw new Error(error.message || 'Login failed');
        }
    }

    async register(email, password, fullName, phone) {
        try {
            const userCredential = await createUserWithEmailAndPassword(this.auth, email, password);
            const user = userCredential.user;

            const response = await fetch(`${this.baseUrl}/auth/register`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    email,
                    password,
                    full_name: fullName,
                    phone,
                    firebase_uid: user.uid
                })
            });

            if (!response.ok) {
                const error = await response.json();
                throw new Error(error.message || 'Registration failed');
            }

            const data = await response.json();
            localStorage.setItem('user', JSON.stringify({...data.user, firebaseUser: user }));
            localStorage.setItem('token', data.token);
            return data;
        } catch (error) {
            throw new Error(error.message || 'Registration failed');
        }
    }

    async resetPassword(email) {
        try {
            await sendPasswordResetEmail(this.auth, email);
            return true;
        } catch (error) {
            console.error('Password reset error:', error);
            throw error;
        }
    }

    async logout() {
        try {
            await signOut(this.auth);
            localStorage.removeItem('user');
            localStorage.removeItem('token');
            window.location.href = '/auth/login.html';
        } catch (error) {
            console.error('Logout error:', error);
            throw error;
        }
    }

    updateUIState(user) {
        const loginForm = document.getElementById('loginForm');
        const userInfo = document.getElementById('userInfo');

        if (loginForm && userInfo) {
            if (user) {
                loginForm.style.display = 'none';
                userInfo.style.display = 'block';
                userInfo.querySelector('.user-email').textContent = user.email;
            } else {
                loginForm.style.display = 'block';
                userInfo.style.display = 'none';
            }
        }
    }

    getCurrentUser() {
        const user = localStorage.getItem('user');
        return user ? JSON.parse(user) : null;
    }

    getToken() {
        return localStorage.getItem('token');
    }

    isAuthenticated() {
        return !!this.getToken() && !!this.currentUser;
    }
}

// Initialize auth service
const authService = new AuthService();
export default authService;