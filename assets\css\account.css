/* General Styles */

:root {
    --primary-color: #4a90e2;
    --secondary-color: #2c3e50;
    --accent-color: #e74c3c;
    --text-color: #ffffff;
    --background-color: #1a1a1a;
    --card-background: rgba(255, 255, 255, 0.05);
    --border-color: rgba(255, 255, 255, 0.1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background: var(--background-color);
    color: var(--text-color);
    min-height: 100vh;
    line-height: 1.6;
}

.page-wrapper {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
}


/* Header Styles */

header {
    margin-bottom: 2rem;
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
}

.logo {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--text-color);
    text-decoration: none;
}

.nav-links {
    display: flex;
    gap: 2rem;
}

.nav-links a {
    color: var(--text-color);
    text-decoration: none;
    transition: color 0.3s ease;
}

.nav-links a:hover,
.nav-links a.active {
    color: var(--primary-color);
}


/* Account Container Styles */

.account-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

section {
    background: var(--card-background);
    backdrop-filter: blur(10px);
    border: 1px solid var(--border-color);
    border-radius: 20px;
    padding: 2rem;
    transition: transform 0.3s ease;
}

section:hover {
    transform: translateY(-5px);
}

h2 {
    margin-bottom: 1.5rem;
    color: var(--primary-color);
}

.content-section {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.info-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.info-group label {
    color: var(--text-color);
    opacity: 0.7;
    font-size: 0.9rem;
}


/* Button Styles */

.edit-btn,
.add-btn,
.save-btn,
.cancel-btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.edit-btn,
.add-btn {
    background: var(--primary-color);
    color: white;
}

.save-btn {
    background: var(--primary-color);
    color: white;
}

.cancel-btn {
    background: var(--secondary-color);
    color: white;
}

.edit-btn:hover,
.add-btn:hover,
.save-btn:hover {
    background: #357abd;
    transform: translateY(-2px);
}

.cancel-btn:hover {
    background: #1a252f;
    transform: translateY(-2px);
}


/* Modal Styles */

.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    backdrop-filter: blur(5px);
}

.modal-content {
    position: relative;
    background: var(--card-background);
    backdrop-filter: blur(10px);
    border: 1px solid var(--border-color);
    border-radius: 20px;
    width: 90%;
    max-width: 500px;
    margin: 50px auto;
    padding: 2rem;
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        transform: translateY(-20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}


/* Form Styles */

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--text-color);
}

.form-group input {
    width: 100%;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid var(--border-color);
    border-radius: 10px;
    color: var(--text-color);
    transition: border-color 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: var(--primary-color);
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
}


/* Address Card Styles */

.address-card {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid var(--border-color);
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    position: relative;
}

.address-card .delete-btn {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: var(--accent-color);
    color: white;
    border: none;
    border-radius: 5px;
    padding: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.address-card .delete-btn:hover {
    background: #c0392b;
    transform: scale(1.1);
}


/* Order Card Styles */

.order-card {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid var(--border-color);
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 1rem;
}

.order-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.order-items {
    display: grid;
    gap: 1rem;
}


/* Favorite Card Styles */

.favorite-card {
    display: flex;
    align-items: center;
    gap: 1rem;
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid var(--border-color);
    border-radius: 15px;
    padding: 1rem;
    margin-bottom: 1rem;
}

.favorite-card img {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 10px;
}

.favorite-info {
    flex: 1;
}

.favorite-card .remove-btn {
    background: var(--accent-color);
    color: white;
    border: none;
    border-radius: 5px;
    padding: 0.5rem 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.favorite-card .remove-btn:hover {
    background: #c0392b;
    transform: scale(1.1);
}


/* Responsive Design */

@media (max-width: 768px) {
    .page-wrapper {
        padding: 1rem;
    }
    .account-container {
        grid-template-columns: 1fr;
    }
    .nav-links {
        gap: 1rem;
    }
    .modal-content {
        width: 95%;
        margin: 20px auto;
    }
    .form-actions {
        flex-direction: column;
    }
    .form-actions button {
        width: 100%;
    }
}


/* Loading States */

.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
}

.loading::after {
    content: '';
    width: 30px;
    height: 30px;
    border: 3px solid var(--border-color);
    border-top-color: var(--primary-color);
    border-radius: 50%;
    animation: loading 1s infinite linear;
}

@keyframes loading {
    to {
        transform: rotate(360deg);
    }
}


/* Orders Section */

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding: 1rem 1.5rem;
    background: var(--card-background);
    border-radius: 10px;
}

.section-header h2 {
    margin: 0;
    font-size: 1.5rem;
    color: var(--text-color);
}

.btn-all-orders {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-all-orders:hover {
    background: #357abd;
    transform: translateY(-2px);
}

.btn-all-orders i {
    font-size: 0.8rem;
}

.orders-grid {
    display: grid;
    gap: 1.5rem;
}

.order-card {
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: 10px;
    overflow: hidden;
}

.order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.order-id h3 {
    margin: 0;
    font-size: 1.1rem;
    color: var(--text-color);
}

.order-date {
    display: block;
    font-size: 0.9rem;
    color: #666;
    margin-top: 0.25rem;
}

.status-badge {
    padding: 0.4rem 1rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
    text-transform: capitalize;
}

.status-pending {
    background: #fff3cd;
    color: #856404;
}

.status-processing {
    background: #cce5ff;
    color: #004085;
}

.status-shipped {
    background: #d4edda;
    color: #155724;
}

.status-delivered {
    background: #d1e7dd;
    color: #0f5132;
}

.status-cancelled {
    background: #f8d7da;
    color: #721c24;
}

.order-content {
    padding: 1.5rem;
}

.order-items {
    display: grid;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.order-item {
    display: flex;
    gap: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
}

.item-image {
    width: 80px;
    height: 80px;
    border-radius: 8px;
    overflow: hidden;
}

.item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.item-details {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.item-details h4 {
    margin: 0;
    font-size: 1rem;
    color: var(--text-color);
}

.item-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 0.5rem;
}

.quantity {
    color: #666;
    font-size: 0.9rem;
}

.price {
    color: var(--primary-color);
    font-weight: 500;
}

.order-summary {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

.order-total {
    font-size: 1.1rem;
}

.order-total .amount {
    color: var(--primary-color);
    font-weight: 500;
    margin-left: 0.5rem;
}

.order-actions {
    display: flex;
    gap: 0.75rem;
}

.btn {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.btn i {
    font-size: 0.8rem;
}

.btn-outline {
    background: transparent;
    border: 1px solid var(--primary-color);
    color: var(--primary-color);
}

.btn-outline:hover {
    background: var(--primary-color);
    color: white;
}

.btn-danger {
    background: transparent;
    border: 1px solid #dc3545;
    color: #dc3545;
}

.btn-danger:hover {
    background: #dc3545;
    color: white;
}

.btn-track {
    background: transparent;
    border: 1px solid #28a745;
    color: #28a745;
}

.btn-track:hover {
    background: #28a745;
    color: white;
}


/* Tracking Modal */

.tracking-modal .modal-content {
    max-width: 600px;
}

.tracking-timeline {
    padding: 1rem 0;
}

.tracking-event {
    display: flex;
    gap: 1.5rem;
    padding: 1rem 0;
    position: relative;
}

.tracking-event:not(:last-child)::after {
    content: '';
    position: absolute;
    left: 15px;
    top: 45px;
    bottom: 0;
    width: 2px;
    background: var(--border-color);
}

.event-icon {
    width: 32px;
    height: 32px;
    background: var(--card-background);
    border: 2px solid var(--border-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1;
}

.tracking-event.completed .event-icon {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.event-details {
    flex: 1;
}

.event-details h4 {
    margin: 0 0 0.25rem;
    color: var(--text-color);
}

.event-details p {
    margin: 0;
    color: #666;
    font-size: 0.9rem;
}

.event-date {
    display: block;
    color: #666;
    font-size: 0.85rem;
    margin-top: 0.25rem;
}

.estimated-delivery {
    margin-top: 1.5rem;
    padding: 1rem;
    background: rgba(40, 167, 69, 0.1);
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: #28a745;
}


/* No Orders State */

.no-orders {
    text-align: center;
    padding: 3rem;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.no-orders i {
    font-size: 3rem;
    color: #6c757d;
    margin-bottom: 1rem;
}

.no-orders h3 {
    margin: 0 0 0.5rem;
    color: #333;
}

.no-orders p {
    color: #666;
    margin-bottom: 1.5rem;
}


/* Order Details Modal */

.order-details-modal .modal-content {
    max-width: 800px;
    width: 90%;
}

.order-details-modal .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 1rem;
    border-bottom: 1px solid #eee;
}

.order-details-modal .modal-body {
    padding: 1.5rem 0;
}

.order-info {
    margin-bottom: 2rem;
}

.order-info p {
    margin: 0.5rem 0;
    color: #666;
}

.order-items-detailed {
    margin-bottom: 2rem;
}

.order-items-detailed h3 {
    margin-bottom: 1rem;
    color: #333;
}

.order-item-detailed {
    display: flex;
    gap: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 6px;
    margin-bottom: 1rem;
}

.order-item-detailed img {
    width: 100px;
    height: 100px;
    object-fit: cover;
    border-radius: 4px;
}

.item-details {
    flex: 1;
}

.item-details h4 {
    margin: 0 0 0.5rem;
    color: #333;
}

.item-details p {
    margin: 0.25rem 0;
    color: #666;
}

.order-total-detailed {
    text-align: right;
    padding-top: 1rem;
    border-top: 1px solid #eee;
}

.order-total-detailed h3 {
    color: #28a745;
    margin: 0;
}