{"name": "base64url", "version": "3.0.1", "description": "For encoding to/from base64urls", "main": "index.js", "types": "./dist/base64url.d.ts", "files": ["dist/", "index.js"], "scripts": {"build": "tsc", "clean": "rm -f dist/*", "test": "npm run clean && npm run build && tap test/*.test.js", "prepublishOnly": "npm run test"}, "repository": {"type": "git", "url": "git://github.com/brianloveswords/base64url.git"}, "keywords": ["base64", "base64url"], "author": "<PERSON>", "license": "MIT", "readmeFilename": "README.md", "engines": {"node": ">=6.0.0"}, "devDependencies": {"@types/node": "^10.0.0", "tap": "^12.1.0"}}