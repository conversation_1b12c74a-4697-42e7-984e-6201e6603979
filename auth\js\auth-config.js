// Firebase configuration
const firebaseConfig = {
    apiKey: "AIzaSyAGVoqQlSR15zRCrlJqYsvz7b2FVOO8Kog",
    authDomain: "project-1-ee121.firebaseapp.com",
    projectId: "project-1-ee121",
    storageBucket: "project-1-ee121.firebasestorage.app",
    messagingSenderId: "737534293699",
    appId: "1:737534293699:web:7a5d1c89e7406b0f1e5476"
};

// Initialize Firebase
import { initializeApp } from "firebase/app";
const app = initializeApp(firebaseConfig);

// API configuration
const API_BASE_URL = 'http://localhost:3000/api';

// Export configuration
const config = {
    firebase: app,
    apiBaseUrl: API_BASE_URL,
    endpoints: {
        login: `${API_BASE_URL}/auth/login`,
        register: `${API_BASE_URL}/auth/register`,
        resetPassword: `${API_BASE_URL}/auth/reset-password`,
        user: (userId) => `${API_BASE_URL}/users/${userId}`,
        addresses: (userId) => `${API_BASE_URL}/users/${userId}/addresses`,
        orders: (userId) => `${API_BASE_URL}/users/${userId}/orders`
    }
};

export default config;