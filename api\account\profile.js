import express from 'express';
import { createConnection } from '../../config/database.js';
import auth from '../middleware/auth.js';

const router = express.Router();

// Get user profile
router.get('/', auth, async (req, res) => {
  const connection = await createConnection();
  
  try {
    const [rows] = await connection.execute(
      'SELECT id, firstName, lastName, email, phone FROM users WHERE id = ?',
      [req.user.id]
    );

    if (rows.length === 0) {
      return res.status(404).json({ message: 'User not found' });
    }

    res.json(rows[0]);
  } catch (error) {
    console.error('Error fetching profile:', error);
    res.status(500).json({ message: 'Server error' });
  } finally {
    await connection.end();
  }
});

// Update user profile
router.put('/', auth, async (req, res) => {
  const connection = await createConnection();
  const { firstName, lastName, phone } = req.body;
  
  try {
    await connection.execute(
      'UPDATE users SET firstName = ?, lastName = ?, phone = ? WHERE id = ?',
      [firstName, lastName, phone, req.user.id]
    );

    const [updatedUser] = await connection.execute(
      'SELECT id, firstName, lastName, email, phone FROM users WHERE id = ?',
      [req.user.id]
    );

    res.json(updatedUser[0]);
  } catch (error) {
    console.error('Error updating profile:', error);
    res.status(500).json({ message: 'Server error' });
  } finally {
    await connection.end();
  }
});

export default router; 